#!/usr/bin/env python3
"""
埋点数据定时任务调度器
"""

import asyncio
import schedule
import time
from datetime import datetime, date, timedelta
from threading import Thread
from loguru import logger
from utils.analytics_reporter import analytics_reporter


class AnalyticsScheduler:
    """埋点数据定时任务调度器"""
    
    def __init__(self):
        self.running = False
        self.scheduler_thread = None
    
    def start(self):
        """启动定时任务"""
        if self.running:
            logger.warning("埋点定时任务已在运行")
            return
        
        self.running = True
        
        # 设置定时任务
        schedule.every().day.at("23:59").do(self._generate_daily_report)
        schedule.every().hour.do(self._cleanup_old_data)
        
        # 启动调度器线程
        self.scheduler_thread = Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("埋点定时任务调度器已启动")
    
    def stop(self):
        """停止定时任务"""
        self.running = False
        schedule.clear()
        logger.info("埋点定时任务调度器已停止")
    
    def _run_scheduler(self):
        """运行调度器"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"定时任务执行异常: {e}")
                time.sleep(60)
    
    def _generate_daily_report(self):
        """生成每日报告"""
        try:
            yesterday = date.today() - timedelta(days=1)
            
            # 为每个平台生成报告
            platforms = ['onebot', 'wecom', None]  # None 表示全平台
            
            for platform in platforms:
                platform_name = platform or 'all'
                report = analytics_reporter.generate_daily_report(yesterday, platform)
                
                # 导出JSON报告
                filename = f"reports/daily_report_{yesterday.isoformat()}_{platform_name}.json"
                analytics_reporter.export_report_json(report, filename)
                
                # 打印关键指标
                logger.info(f"每日报告 [{platform_name}] - {yesterday}")
                logger.info(f"  活跃用户: {report.active_users}")
                logger.info(f"  新用户: {report.new_users}")
                logger.info(f"  总消息: {report.total_messages}")
                logger.info(f"  语音成功率: {report.voice_success_rate}%")
                
        except Exception as e:
            logger.error(f"生成每日报告失败: {e}")
    
    def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            # 保留30天的详细数据
            cutoff_date = date.today() - timedelta(days=30)
            
            # TODO: 实现数据清理逻辑
            # 可以删除30天前的详细事件数据，保留汇总数据
            
            logger.debug(f"数据清理检查完成，截止日期: {cutoff_date}")
            
        except Exception as e:
            logger.error(f"数据清理失败: {e}")


# 全局调度器实例
analytics_scheduler = AnalyticsScheduler()


def start_analytics_scheduler():
    """启动埋点调度器"""
    analytics_scheduler.start()


def stop_analytics_scheduler():
    """停止埋点调度器"""
    analytics_scheduler.stop()


# 手动生成报告的便捷函数
def generate_report_for_date(target_date: date = None, platform: str = None):
    """为指定日期生成报告"""
    if target_date is None:
        target_date = date.today() - timedelta(days=1)
    
    try:
        report = analytics_reporter.generate_daily_report(target_date, platform)
        platform_name = platform or 'all'
        filename = f"reports/manual_report_{target_date.isoformat()}_{platform_name}.json"
        analytics_reporter.export_report_json(report, filename)
        
        print(f"报告已生成: {filename}")
        print(f"活跃用户: {report.active_users}")
        print(f"新用户: {report.new_users}")
        print(f"总消息: {report.total_messages}")
        print(f"语音成功率: {report.voice_success_rate}%")
        print(f"峰值时段: {report.peak_hours}")
        
        return report
        
    except Exception as e:
        logger.error(f"生成报告失败: {e}")
        return None


def get_user_stats(days: int = 7, platform: str = None):
    """获取用户统计数据"""
    try:
        # 活跃用户趋势
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        daily_stats = []
        for i in range(days):
            current_date = start_date + timedelta(days=i)
            active_users = analytics_reporter.get_daily_active_users(current_date, platform)
            new_users = analytics_reporter.get_new_users_count(current_date, platform)
            daily_stats.append({
                'date': current_date.isoformat(),
                'active_users': active_users,
                'new_users': new_users
            })
        
        # 对话统计
        conversation_stats = analytics_reporter.get_conversation_stats(end_date, platform)
        
        # 重置频率
        reset_stats = analytics_reporter.get_reset_frequency(days, platform)
        
        # AI切换统计
        ai_switch_stats = analytics_reporter.get_ai_switch_stats(days, platform)
        
        return {
            'period_days': days,
            'platform': platform or 'all',
            'daily_trends': daily_stats,
            'conversation_stats': conversation_stats,
            'reset_stats': reset_stats,
            'ai_switch_stats': ai_switch_stats
        }
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {e}")
        return None


def get_voice_analytics(days: int = 7, platform: str = None):
    """获取语音功能分析数据"""
    try:
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        daily_voice_stats = []
        for i in range(days):
            current_date = start_date + timedelta(days=i)
            voice_stats = analytics_reporter.get_voice_stats(current_date, platform)
            daily_voice_stats.append({
                'date': current_date.isoformat(),
                **voice_stats
            })
        
        # 计算总体统计
        total_attempts = sum(stat['total_attempts'] for stat in daily_voice_stats)
        total_success = sum(stat['success_count'] for stat in daily_voice_stats)
        total_fallback = sum(stat['fallback_count'] for stat in daily_voice_stats)
        total_errors = sum(stat['error_count'] for stat in daily_voice_stats)
        
        overall_success_rate = (total_success / total_attempts * 100) if total_attempts > 0 else 0
        
        return {
            'period_days': days,
            'platform': platform or 'all',
            'daily_stats': daily_voice_stats,
            'overall_stats': {
                'total_attempts': total_attempts,
                'total_success': total_success,
                'total_fallback': total_fallback,
                'total_errors': total_errors,
                'success_rate': round(overall_success_rate, 2)
            }
        }
        
    except Exception as e:
        logger.error(f"获取语音分析失败: {e}")
        return None


if __name__ == "__main__":
    # 测试功能
    print("=== 埋点数据分析测试 ===")
    
    # 生成昨天的报告
    yesterday_report = generate_report_for_date()
    
    # 获取7天用户统计
    user_stats = get_user_stats(7)
    if user_stats:
        print(f"\n7天用户统计:")
        print(f"总活跃用户: {sum(day['active_users'] for day in user_stats['daily_trends'])}")
        print(f"总新用户: {sum(day['new_users'] for day in user_stats['daily_trends'])}")
    
    # 获取语音分析
    voice_analytics = get_voice_analytics(7)
    if voice_analytics:
        print(f"\n7天语音统计:")
        print(f"总尝试次数: {voice_analytics['overall_stats']['total_attempts']}")
        print(f"成功率: {voice_analytics['overall_stats']['success_rate']}%")
