# Bot埋点系统设计文档

## 概述

本埋点系统为loveBot1.2设计，用于收集和分析用户行为数据、系统性能指标和业务关键指标，为产品优化和运营决策提供数据支持。

## 埋点数据分类

### 1. 语音相关埋点

#### 语音功能埋点
- **语音生成成功率**：GPT-SoVITS 成功/失败次数及耗时
- **语音回退使用率**：使用 ceshi.wav 回退的次数和原因
- **语音策略触发率**：V0策略触发/跳过的次数和概率
- **语音请求使用率**：用户主动要求语音的次数和模式
- **语音发送失败率**：平台发送语音失败的次数和错误类型

#### 语音错误埋点
- **GPT-SoVITS 服务错误**：连接超时、服务不可用、模型错误
- **音频转换错误**：格式转换失败（AMR、Silk等）
- **平台发送错误**：QQ、企业微信发送失败
- **配置错误**：模型不存在、配置异常等

### 2. Bot服务相关埋点

#### 用户活跃度埋点
- **单日活跃用户数**：每日发送消息的唯一用户数
- **每日新添加用户数**：首次使用bot的用户数（通过limit数据库判断）
- **用户留存率**：7日、30日留存统计
- **用户高频在线时间段**：按小时统计用户活跃度分布

#### 对话质量埋点
- **一次聊天的对话轮数**：从开始到重置/超时的对话数
- **平均响应时间**：AI处理请求的耗时统计
- **重置会话频率**：用户使用重置命令的次数和频率
- **错误率统计**：各类错误的发生频率和分布

#### 功能使用埋点
- **AI模型切换频率**：用户切换不同AI的次数和偏好
- **绘图功能使用率**：画图命令的使用统计
- **特殊命令使用率**：各种系统命令的使用频率

## 技术架构

### 数据库设计

#### 1. 用户活跃度表 (user_activity)
```sql
CREATE TABLE user_activity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,                -- 用户ID
    platform TEXT NOT NULL,              -- 平台（onebot/wecom）
    activity_date DATE NOT NULL,         -- 活跃日期
    message_count INTEGER DEFAULT 0,     -- 消息数量
    conversation_rounds INTEGER DEFAULT 0, -- 对话轮数
    first_seen_date DATE,                -- 首次见到日期
    last_active_time DATETIME,           -- 最后活跃时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, activity_date)
);
```

#### 2. 语音统计表 (voice_stats)
```sql
CREATE TABLE voice_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,               -- 用户ID
    platform TEXT NOT NULL,             -- 平台
    event_type TEXT NOT NULL,            -- 事件类型（success/fallback/error/request）
    event_detail TEXT,                   -- 事件详情
    voice_mode TEXT,                     -- 语音模式（strategy/request/always）
    error_type TEXT,                     -- 错误类型
    processing_time_ms INTEGER,          -- 处理耗时（毫秒）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 系统事件表 (system_events)
```sql
CREATE TABLE system_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,               -- 用户ID
    platform TEXT NOT NULL,             -- 平台
    event_type TEXT NOT NULL,            -- 事件类型（reset/switch_ai/draw/error）
    event_detail TEXT,                   -- 事件详情
    session_id TEXT,                     -- 会话ID
    processing_time_ms INTEGER,          -- 处理耗时
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. 每日统计汇总表 (daily_summary)
```sql
CREATE TABLE daily_summary (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    summary_date DATE NOT NULL,          -- 统计日期
    platform TEXT NOT NULL,             -- 平台
    active_users INTEGER DEFAULT 0,     -- 活跃用户数
    new_users INTEGER DEFAULT 0,        -- 新用户数
    total_messages INTEGER DEFAULT 0,   -- 总消息数
    total_conversations INTEGER DEFAULT 0, -- 总对话数
    voice_success_rate DECIMAL(5,2),    -- 语音成功率
    avg_response_time_ms INTEGER,       -- 平均响应时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(summary_date, platform)
);
```

### 核心模块

#### 1. AnalyticsManager (chatgpt/utils/analytics.py)
- **功能**：埋点事件收集和存储
- **特性**：
  - 异步事件队列处理
  - 批量数据插入优化
  - 自动数据库初始化
  - 线程安全设计

#### 2. AnalyticsReporter (chatgpt/utils/analytics_reporter.py)
- **功能**：数据查询和分析报告
- **特性**：
  - 多维度数据查询
  - 自动计算统计指标
  - JSON格式报告导出
  - 灵活的时间范围查询

#### 3. AnalyticsScheduler (chatgpt/utils/analytics_scheduler.py)
- **功能**：定时任务和报告生成
- **特性**：
  - 每日自动报告生成
  - 数据清理任务
  - 手动报告生成接口
  - 多平台报告支持

## 埋点实现

### 语音相关埋点

#### 在 text_to_speech.py 中添加：
```python
# 语音成功
await track_voice_success(user_id, platform, "gptsovits", processing_time)

# 语音回退
await track_voice_fallback(user_id, platform, "GPT-SoVITS服务不可用")

# 语音错误
await track_voice_error(user_id, platform, "service_error", "连接失败")
```

#### 在 universal.py 中添加：
```python
# 重置会话
await track_system_event(user_id, platform, EventType.RESET_SESSION.value, 
                        "用户主动重置会话", session_id)

# 消息发送
await track_system_event(user_id, platform, EventType.MESSAGE_SENT.value, 
                        f"消息长度: {len(str(rendered))}", session_id)
```

## 数据分析功能

### 1. 实时指标查询
- 单日活跃用户数
- 语音功能成功率
- 系统错误率
- 用户峰值时间段

### 2. 趋势分析
- 7日/30日用户增长趋势
- 语音功能使用趋势
- 错误率变化趋势
- 功能使用偏好分析

### 3. 自动报告
- 每日23:59自动生成报告
- JSON格式便于集成
- 多平台分别统计
- 关键指标邮件提醒（可扩展）

## 使用方法

### 1. 系统初始化
```python
from chatgpt.utils.analytics import analytics_manager
from chatgpt.utils.analytics_scheduler import start_analytics_scheduler

# 初始化数据库
analytics_manager.init_database()

# 启动定时任务
start_analytics_scheduler()
```

### 2. 手动查询数据
```python
from chatgpt.utils.analytics_reporter import analytics_reporter

# 查询今日活跃用户
active_users = analytics_reporter.get_daily_active_users()

# 查询语音统计
voice_stats = analytics_reporter.get_voice_stats()

# 生成报告
report = analytics_reporter.generate_daily_report()
```

### 3. 查看报告
- 报告文件：`reports/daily_report_YYYY-MM-DD_platform.json`
- 数据库文件：`analytics.db`
- 日志输出：实时关键指标

## 性能优化

### 1. 异步处理
- 埋点事件异步入队
- 批量数据库操作
- 非阻塞数据处理

### 2. 存储优化
- 索引优化查询性能
- 定期数据清理
- 汇总表减少计算

### 3. 内存管理
- 事件队列大小限制
- 及时释放临时数据
- 数据库连接池管理

## 扩展性设计

### 1. 新埋点添加
- 继承 AnalyticsEvent 数据结构
- 添加对应的 track_* 便捷函数
- 更新数据库表结构

### 2. 新平台支持
- 平台字段支持任意值
- 自动适配新平台统计
- 独立的平台报告

### 3. 新分析维度
- 扩展 AnalyticsReporter 查询方法
- 添加新的统计指标
- 自定义报告格式

## 监控和告警

### 1. 系统健康监控
- 埋点系统运行状态
- 数据库连接状态
- 队列处理性能

### 2. 业务指标告警
- 语音成功率低于阈值
- 错误率异常增长
- 用户活跃度异常下降

### 3. 数据质量保证
- 数据完整性检查
- 异常数据过滤
- 数据一致性验证

## 隐私和安全

### 1. 数据脱敏
- 用户ID哈希处理（可选）
- 敏感信息过滤
- 数据访问权限控制

### 2. 数据保留策略
- 详细数据保留30天
- 汇总数据长期保留
- 自动数据清理

### 3. 合规要求
- 符合数据保护法规
- 用户数据使用透明
- 数据删除机制

## 总结

本埋点系统为loveBot1.2提供了全面的数据收集和分析能力，支持：

- ✅ **语音功能监控**：成功率、错误类型、性能指标
- ✅ **用户行为分析**：活跃度、留存率、使用偏好
- ✅ **系统性能监控**：响应时间、错误率、资源使用
- ✅ **业务指标跟踪**：功能使用率、用户增长、质量指标
- ✅ **自动化报告**：定时生成、多维分析、趋势预测

通过这套埋点系统，可以实现数据驱动的产品优化和运营决策。
