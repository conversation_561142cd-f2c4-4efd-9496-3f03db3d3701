# GPT-SoVITS 语音功能集成说明

## 功能概述

本项目已成功集成 GPT-SoVITS 语音合成功能，能够将 AI 生成的文字回复转换为语音，并同时发送文字和语音内容给用户。

## 主要特性

### 1. 智能语音模型选择
- 根据当前男主自动选择对应的语音模型
- 支持 11 种不同的男主语音模型：
  - `ls` → `lishen`
  - `lzy` → `lizeyan`
  - `66` → `luchen`
  - `qc` → `qinche`
  - `77` → `qisili`
  - `qy` → `qiyu`
  - `sxh` → `shenxinghui`
  - `00` → `xiamingxing`
  - `11` → `xiaoyi`
  - `xyz` → `xiayizhou`
  - `55` → `zhalisu`
- 默认使用 `luchen` 模型

### 2. 回退机制
- 当 GPT-SoVITS 服务不可用时，自动使用项目根目录下的 `ceshi.wav` 文件
- 确保语音功能的可靠性

### 3. 多平台支持
- 支持 OneBot (QQ) 平台
- 支持 企业微信 (WeCom) 平台
- 自动适配不同平台的语音格式要求

## 文件结构

```
loveBot1.2/
├── chatgpt/
│   ├── utils/
│   │   ├── gptsovits_tts.py          # GPT-SoVITS 语音合成核心模块
│   │   └── text_to_speech.py         # TTS 系统集成（已修改）
│   ├── config.py                     # 配置文件（已修改）
│   ├── universal.py                  # 消息处理逻辑（已修改）
│   └── platforms/
│       ├── onebot_bot.py             # QQ 平台（支持语音发送）
│       └── wecom_bot.py              # 企业微信平台（支持语音发送）
├── ceshi.wav                         # 回退语音文件
└── test_gptsovits.py                 # 测试脚本
```

## 配置说明

### 1. TTS 引擎配置
在 `chatgpt/config.py` 中，TTS 引擎已设置为 `gptsovits`：

```python
class TextToSpeech(BaseModel):
    engine: str = "gptsovits"  # 使用 GPT-SoVITS 引擎
```

### 2. GPT-SoVITS 服务地址
默认服务地址为 `http://localhost:9000`，可在 `chatgpt/utils/gptsovits_tts.py` 中修改：

```python
def __init__(self):
    self.base_url = "http://localhost:9000"  # 修改为实际服务地址
```

## 使用方法

### 1. 启动 GPT-SoVITS 服务
按照 `README_for_gptsovits.md` 的说明启动 GPT-SoVITS 服务器。

### 2. 语音功能自动启用
- 当 TTS 引擎设置为 `gptsovits` 时，语音功能会自动启用
- AI 的每个回复都会同时生成文字和语音

### 3. 语音切换命令
用户可以通过发送语音切换命令来控制语音功能：
- 发送 "切换语音 关闭" 可以关闭语音功能
- 发送 "切换语音 开启" 可以重新启用语音功能

## API 接口

### GPT-SoVITS 请求格式
```json
{
    "model_name": "luchen",
    "text": "要合成的文本内容",
    "text_language": "zh"
}
```

### 响应格式
- 成功：返回 WAV 格式的音频数据
- 失败：返回错误状态码

## 测试

运行测试脚本验证功能：

```bash
python test_gptsovits.py
```

测试内容包括：
1. 男主到模型的映射测试
2. GPT-SoVITS 服务连接测试
3. 回退机制测试

## 故障排除

### 1. GPT-SoVITS 服务连接失败
- 检查服务是否正常运行在 `localhost:9000`
- 检查防火墙设置
- 系统会自动使用回退语音文件

### 2. 回退语音文件不存在
- 确保项目根目录下存在 `ceshi.wav` 文件
- 文件大小应为 91730 bytes

### 3. 语音发送失败
- 检查平台配置是否正确
- 查看日志文件获取详细错误信息

## 开发说明

### 核心类：GPTSoVITSTTS
位于 `chatgpt/utils/gptsovits_tts.py`

主要方法：
- `get_model_name()`: 根据当前男主获取语音模型名称
- `synthesize_speech()`: 合成语音
- `get_fallback_voice()`: 获取回退语音文件

### 集成点
- `chatgpt/utils/text_to_speech.py`: TTS 系统集成
- `chatgpt/universal.py`: 消息处理流程
- `chatgpt/platforms/`: 平台特定的语音发送逻辑

## 注意事项

1. **服务依赖**：需要 GPT-SoVITS 服务正常运行
2. **网络超时**：默认 30 秒超时，可根据需要调整
3. **文件清理**：临时语音文件会自动清理
4. **错误处理**：完善的异常处理和日志记录

## 企业微信 AMR 音频支持

### 音频格式优先级
1. **AMR 格式**（最佳）：企业微信原生支持，音质好，文件小
2. **WAV 格式**（备选）：企业微信支持，兼容性好
3. **原始格式**（兜底）：直接发送原始音频文件

### 转换流程
```
音频输入 → 检测格式 → 转换为8kHz单声道 → 导出AMR → 企业微信语音消息
    ↓ (AMR失败)
转换为WAV → 企业微信语音消息
    ↓ (WAV失败)
原始文件 → 企业微信文件消息
```

### ffmpeg 依赖
- **必需**：AMR 格式转换需要 ffmpeg
- **安装**：下载并添加到系统 PATH
- **验证**：运行 `python test_wecom_amr.py` 测试

### 平台适配优化
- **企业微信平台**：优先 AMR，备选 WAV，兜底原始文件
- **QQ 平台**：使用 Silk 格式
- **其他平台**：使用 WAV 格式


## 语音回退策略

### 回退机制控制
新增配置参数控制回退语音文件的使用：

```toml
[text_to_speech]
voice_fallback_enabled = true    # 是否启用回退语音文件（ceshi.wav）
```

### 工作流程
1. **优先使用 GPT-SoVITS**：尝试正常语音合成
2. **服务失败时**：检查 `voice_fallback_enabled` 配置
3. **回退启用**：使用 ceshi.wav 作为回退语音
4. **回退禁用**：跳过语音，只发送文字

### 智能文字发送
确保用户始终能收到回复内容：

**核心逻辑**：
- **语音成功 + 只发语音配置**：只发语音
- **语音失败 + 只发语音配置**：强制发送文字
- **既发语音又发文字配置**：总是发送文字

**状态识别**：
- 正常语音：GPT-SoVITS 成功生成
- 回退语音：使用 ceshi.wav 文件
- 语音失败：完全没有语音输出

### 配置建议
```toml
# 推荐配置：平衡模式
voice_fallback_enabled = true    # 启用回退，提高可用性
voice_with_text = 0              # 优先语音，失败时自动发文字

# 高可靠模式
voice_fallback_enabled = true    # 启用回退
voice_with_text = 1              # 总是双重发送

# 纯语音模式（不推荐）
voice_fallback_enabled = false   # 禁用回退
voice_with_text = 0              # 只发语音，可能导致无回复
```

## 更新日志

- 2024-06-09: 完成 GPT-SoVITS 语音功能集成
  - 新增 GPTSoVITSTTS 类
  - 修改 TTS 系统支持 GPT-SoVITS
  - 添加回退机制
  - 更新配置和消息处理逻辑
  - 修复 Windows 系统音频转换问题
  - 优化企业微信平台语音发送逻辑

- 2024-06-24: 语音策略和回退机制优化
  - 新增语音策略V0（概率触发）
  - 新增语音请求策略（用户主动要求）
  - 新增语音回退控制配置
  - 实现智能文字发送逻辑
  - 确保用户始终能收到回复内容
