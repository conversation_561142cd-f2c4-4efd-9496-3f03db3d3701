# 语音策略V0配置说明

交互设计策略V0：  

● 触发条件：  
  [√] 当字符数量大于50，小于280的时候，有60%的概率触发语音（模拟的是说话太多的时候）  
  [√] 当字符数量大于4，小于等于50的时候，有40%的概率触发语音（模拟的是不方便的时候）  
  [√] 这两个概率值也可以在config.cfg中配置  
  [√] 当被用户很明显要求说语音的时候，使用语音回复（例如用户发言是：你可以语音回复我吗？）  
● 禁止条件：  
  [√] bot回复写死的系统提示时，不发语音（例如回复‘会话已重置’之类的则不发语音）  
● 交互/显示方案：  
  [√] 默认为：发了语音就不发对应的文字了。  
  [√] 但是给config.cfg文件里面增加一个参数，参数为0则发了语音就不发对应的文字了。参数为1则既发语音，也发对应的文字。默认为0.  
● 错误情况：如果语音没能获得成功  
  [√] 使用回退语音文件，内容为“chatailover组织正在测试中”  

## 配置文件位置
在 `config.cfg` 文件的 `[text_to_speech]` 部分添加以下配置：

## 配置参数

```toml
[text_to_speech]
# 基础配置
always = false
engine = "gptsovits"
default = "zh-CN-XiaoxiaoNeural"
default_voice_prefix = ["zh-CN", "zh-TW"]

# 语音策略V0配置
voice_strategy_enabled = true    # 是否启用语音策略V0
voice_probability_short = 0.4    # 短文本语音概率（字符数 > 4 且 ≤ 50）
voice_probability_medium = 0.6   # 中等文本语音概率（字符数 > 50 且 ≤ 280）
voice_with_text = 0              # 语音交互方案：0=只发语音不发文字，1=既发语音又发文字

# 用户主动语音请求策略配置
voice_request_enabled = true     # 是否启用语音请求功能（用户主动要求语音回复）
voice_request_count = 5          # 语音请求后的回复条数
voice_request_with_text = 1      # 语音请求模式下是否同时发送文字：0=只发语音，1=既发语音又发文字

# 语音回退策略配置
voice_fallback_enabled = true    # 是否启用语音回退文件（ceshi.wav）：0=不使用回退语音，1=使用回退语音
```

## 参数详细说明

### voice_strategy_enabled
- **类型**: boolean
- **默认值**: true
- **说明**: 是否启用语音策略V0
- **效果**: 
  - `true`: 启用策略，根据字符数和概率决定是否发送语音
  - `false`: 禁用策略，使用原始逻辑（总是发送语音）

### voice_probability_short
- **类型**: float (0.0 - 1.0)
- **默认值**: 0.4
- **说明**: 短文本的语音触发概率
- **适用范围**: 字符数 > 4 且 ≤ 50
- **示例**: 0.4 表示 40% 的概率触发语音

### voice_probability_medium
- **类型**: float (0.0 - 1.0)
- **默认值**: 0.6
- **说明**: 中等文本的语音触发概率
- **适用范围**: 字符数 > 50 且 ≤ 280
- **示例**: 0.6 表示 60% 的概率触发语音

### voice_with_text
- **类型**: integer (0 或 1)
- **默认值**: 0
- **说明**: 语音交互方案
- **选项**:
  - `0`: 只发语音，不发对应的文字
  - `1`: 既发语音，也发对应的文字

### voice_request_enabled
- **类型**: boolean
- **默认值**: true
- **说明**: 是否启用语音请求功能
- **效果**:
  - `true`: 用户可以主动要求语音回复
  - `false`: 禁用语音请求功能

### voice_request_count
- **类型**: integer
- **默认值**: 5
- **说明**: 语音请求后的回复条数
- **范围**: 建议 1-10 条
- **示例**: 5 表示启用语音请求后，接下来 5 条回复都使用语音

### voice_request_with_text
- **类型**: integer (0 或 1)
- **默认值**: 1
- **说明**: 语音请求模式下的交互方案
- **选项**:
  - `0`: 只发语音，不发对应的文字（语音失败时强制发文字）
  - `1`: 既发语音，也发对应的文字

### voice_fallback_enabled
- **类型**: boolean
- **默认值**: true
- **说明**: 是否启用语音回退文件（ceshi.wav）
- **效果**:
  - `true`: 当 GPT-SoVITS 服务不可用时，使用 ceshi.wav 作为回退语音
  - `false`: 当 GPT-SoVITS 服务不可用时，不使用回退语音，只发送文字
- **注意**: 即使设置为 `false`，如果配置为只发语音（voice_with_text=0），语音失败时仍会强制发送文字

## 触发规则

### 字符数统计
- 只统计 `Plain` 文本元素的字符数
- 自动去除首尾空白字符
- 不包括图片、表情等非文本元素

### 触发条件
1. **短文本**: 4 < 字符数 ≤ 50
   - 触发概率: `voice_probability_short`
   - 适用场景: 简短回复、问候语等

2. **中等文本**: 50 < 字符数 ≤ 280
   - 触发概率: `voice_probability_medium`
   - 适用场景: 详细回答、解释说明等

3. **其他情况**: 字符数 ≤ 4 或 > 280
   - 不触发语音
   - 适用场景: 极短回复或超长文本

4. **语音请求模式**: 用户主动要求语音回复
   - 触发条件: 用户发送语音请求关键词
   - 持续时间: 配置的回复条数
   - 优先级: 高于概率策略

## 用户主动语音请求部分

### 触发关键词
用户发送以下关键词时会启用语音请求模式：

**直接请求类**:
- "语音回复"、"用语音回复"、"语音说"、"语音告诉我"
- "你可以语音回复我吗"、"能语音回复吗"、"可以语音回复吗"
- "说话"、"用声音说"、"用声音回复"

**表达愿望类**:
- "我想听你的声音"、"想听语音"、"想听你说话"
- "语音聊天"、"用语音聊"、"语音对话"

**功能控制类**:
- "开启语音"、"启用语音"、"打开语音"

### 关闭关键词
用户发送以下关键词时会关闭语音请求模式：

- "关闭语音"、"停止语音"、"取消语音"
- "不要语音"、"别语音"、"关掉语音"
- "退出语音"、"结束语音"、"语音关闭"

### 交互流程示例

```
用户: "你可以语音回复我吗？"
Bot: "【系统消息】好的！接下来我会用语音回复你，共5条消息。如需关闭请说'关闭语音'。"

用户: "今天天气怎么样？"
Bot: [语音回复] + 可选文字

用户: "谢谢"
Bot: [语音回复] + 可选文字
... (继续3条)

用户: "关闭语音"
Bot: "好的，语音模式已关闭，我会恢复正常的回复模式。"
```

### 自动关闭机制
- **条数达到**: 发送完配置的条数后自动关闭
- **会话重置**: 用户重置会话时自动清除语音请求状态
- **手动关闭**: 用户发送关闭关键词时立即关闭

## 智能文字发送策略

### 核心原则
确保用户始终能收到回复内容，即使语音生成失败。

### 发送逻辑
1. **语音成功 + 配置只发语音**: 只发语音，不发文字
2. **语音失败 + 配置只发语音**: 强制发送文字，确保用户收到内容
3. **配置既发语音又发文字**: 总是发送文字，无论语音是否成功

### 语音状态判断
- **正常语音**: GPT-SoVITS 服务成功生成的语音
- **回退语音**: 使用 ceshi.wav 回退文件的语音
- **语音失败**: 完全没有语音输出

### 场景示例
```
场景1: GPT-SoVITS正常 + voice_with_text=0
结果: 只发语音 ✅

场景2: GPT-SoVITS失败 + 回退启用 + voice_with_text=0
结果: 发送回退语音 + 强制发送文字 ✅

场景3: GPT-SoVITS失败 + 回退禁用 + voice_with_text=0
结果: 只发送文字 ✅

场景4: 任何情况 + voice_with_text=1
结果: 既发语音又发文字 ✅
```

## 日志输出

系统会输出详细的日志信息，方便调试：

### 语音策略V0日志
```
[语音策略V0] 消息字符数: 25
[语音策略V0] 短文本范围 (4 < 25 ≤ 50)，概率: 0.4
[语音策略V0] 字符数: 25, 概率: 0.4, 随机值: 0.234, 触发语音: 是
[语音策略V0] 只发语音，跳过文字发送
```

### 语音请求日志
```
[语音请求] 检测到语音请求关键词: '语音回复' 在消息: '你可以语音回复我吗？'
[语音请求] 会话 friend-123456 启用语音请求模式，剩余 5 条
[语音请求] 当前处于语音请求模式，剩余 4 条
[语音请求] 会话 friend-123456 剩余语音回复: 3
[语音请求] 语音请求模式已结束
```

## 配置建议

### 推荐配置1：平衡模式
```toml
voice_strategy_enabled = true
voice_probability_short = 0.4
voice_probability_medium = 0.6
voice_with_text = 0
```
- 适合大多数场景
- 短文本适度触发，中等文本较高概率触发
- 只发语音，减少消息冗余

### 推荐配置2：高频语音模式
```toml
voice_strategy_enabled = true
voice_probability_short = 0.7
voice_probability_medium = 0.8
voice_with_text = 0
```
- 适合语音优先的场景
- 更高的语音触发概率
- 只发语音

### 推荐配置3：双重发送模式
```toml
voice_strategy_enabled = true
voice_probability_short = 0.3
voice_probability_medium = 0.5
voice_with_text = 1
```
- 适合需要文字备份的场景
- 较低的语音触发概率
- 既发语音又发文字

### 推荐配置4：禁用策略
```toml
voice_strategy_enabled = false
```
- 回到原始行为
- 总是发送语音（如果语音功能启用）

## 测试方法

1. 修改配置文件
2. 重启机器人
3. 发送不同长度的消息测试
4. 观察日志输出验证策略是否生效

## 写死响应过滤

系统会自动识别并跳过以下类型的"写死响应"，这些响应不会转换为语音：

### 自动过滤的响应类型
- **系统操作响应**: "会话已重置"、"已切换至 xxx"、"语音已启用"等
- **错误提示响应**: "模型不存在！"、"未配置 xxx"、"权限不足"等
- **系统消息**: 以"【系统消息】"、"【系统提示】"开头的消息
- **额度相关**: "额度更新成功！"、"您的使用次数已达上限"等
- **配置相关**: "配置文件重新载入完毕！"、"登录结束"等

### 检测逻辑
- **关键词匹配**: 检测配置文件中定义的固定响应文本
- **模式匹配**: 使用正则表达式识别系统响应模式
- **自动跳过**: 检测到写死响应时自动跳过语音转换

## 注意事项

1. **概率是独立的**: 每次消息都会重新计算概率
2. **字符数计算**: 只计算纯文本，不包括标点符号的特殊处理
3. **配置生效**: 修改配置后需要重启机器人
4. **日志级别**: 确保日志级别足够低以查看调试信息
5. **平台兼容**: 策略适用于所有支持语音的平台（QQ、企业微信等）
6. **写死响应**: 系统响应（如"会话已重置"）自动跳过语音转换
7. **发送顺序**: voice_with_text=0 时确保只发语音不发文字
