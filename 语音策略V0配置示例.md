# 语音策略V0配置说明

## 配置文件位置
在 `config.cfg` 文件的 `[text_to_speech]` 部分添加以下配置：

## 配置参数

```toml
[text_to_speech]
# 基础配置
always = false
engine = "gptsovits"
default = "zh-CN-XiaoxiaoNeural"
default_voice_prefix = ["zh-CN", "zh-TW"]

# 语音策略V0配置
voice_strategy_enabled = true    # 是否启用语音策略V0
voice_probability_short = 0.4    # 短文本语音概率（字符数 > 4 且 ≤ 50）
voice_probability_medium = 0.6   # 中等文本语音概率（字符数 > 50 且 ≤ 280）
voice_with_text = 0              # 语音交互方案：0=只发语音不发文字，1=既发语音又发文字
```

## 参数详细说明

### voice_strategy_enabled
- **类型**: boolean
- **默认值**: true
- **说明**: 是否启用语音策略V0
- **效果**: 
  - `true`: 启用策略，根据字符数和概率决定是否发送语音
  - `false`: 禁用策略，使用原始逻辑（总是发送语音）

### voice_probability_short
- **类型**: float (0.0 - 1.0)
- **默认值**: 0.4
- **说明**: 短文本的语音触发概率
- **适用范围**: 字符数 > 4 且 ≤ 50
- **示例**: 0.4 表示 40% 的概率触发语音

### voice_probability_medium
- **类型**: float (0.0 - 1.0)
- **默认值**: 0.6
- **说明**: 中等文本的语音触发概率
- **适用范围**: 字符数 > 50 且 ≤ 280
- **示例**: 0.6 表示 60% 的概率触发语音

### voice_with_text
- **类型**: integer (0 或 1)
- **默认值**: 0
- **说明**: 语音交互方案
- **选项**:
  - `0`: 只发语音，不发对应的文字
  - `1`: 既发语音，也发对应的文字

## 触发规则

### 字符数统计
- 只统计 `Plain` 文本元素的字符数
- 自动去除首尾空白字符
- 不包括图片、表情等非文本元素

### 触发条件
1. **短文本**: 4 < 字符数 ≤ 50
   - 触发概率: `voice_probability_short`
   - 适用场景: 简短回复、问候语等

2. **中等文本**: 50 < 字符数 ≤ 280
   - 触发概率: `voice_probability_medium`
   - 适用场景: 详细回答、解释说明等

3. **其他情况**: 字符数 ≤ 4 或 > 280
   - 不触发语音
   - 适用场景: 极短回复或超长文本

## 日志输出

系统会输出详细的日志信息，方便调试：

```
[语音策略V0] 消息字符数: 25
[语音策略V0] 短文本范围 (4 < 25 ≤ 50)，概率: 0.4
[语音策略V0] 字符数: 25, 概率: 0.4, 随机值: 0.234, 触发语音: 是
[语音策略V0] 只发语音，跳过文字发送
```

## 配置建议

### 推荐配置1：平衡模式
```toml
voice_strategy_enabled = true
voice_probability_short = 0.4
voice_probability_medium = 0.6
voice_with_text = 0
```
- 适合大多数场景
- 短文本适度触发，中等文本较高概率触发
- 只发语音，减少消息冗余

### 推荐配置2：高频语音模式
```toml
voice_strategy_enabled = true
voice_probability_short = 0.7
voice_probability_medium = 0.8
voice_with_text = 0
```
- 适合语音优先的场景
- 更高的语音触发概率
- 只发语音

### 推荐配置3：双重发送模式
```toml
voice_strategy_enabled = true
voice_probability_short = 0.3
voice_probability_medium = 0.5
voice_with_text = 1
```
- 适合需要文字备份的场景
- 较低的语音触发概率
- 既发语音又发文字

### 推荐配置4：禁用策略
```toml
voice_strategy_enabled = false
```
- 回到原始行为
- 总是发送语音（如果语音功能启用）

## 测试方法

1. 修改配置文件
2. 重启机器人
3. 发送不同长度的消息测试
4. 观察日志输出验证策略是否生效

## 写死响应过滤

系统会自动识别并跳过以下类型的"写死响应"，这些响应不会转换为语音：

### 自动过滤的响应类型
- **系统操作响应**: "会话已重置"、"已切换至 xxx"、"语音已启用"等
- **错误提示响应**: "模型不存在！"、"未配置 xxx"、"权限不足"等
- **系统消息**: 以"【系统消息】"、"【系统提示】"开头的消息
- **额度相关**: "额度更新成功！"、"您的使用次数已达上限"等
- **配置相关**: "配置文件重新载入完毕！"、"登录结束"等

### 检测逻辑
- **关键词匹配**: 检测配置文件中定义的固定响应文本
- **模式匹配**: 使用正则表达式识别系统响应模式
- **自动跳过**: 检测到写死响应时自动跳过语音转换

## 注意事项

1. **概率是独立的**: 每次消息都会重新计算概率
2. **字符数计算**: 只计算纯文本，不包括标点符号的特殊处理
3. **配置生效**: 修改配置后需要重启机器人
4. **日志级别**: 确保日志级别足够低以查看调试信息
5. **平台兼容**: 策略适用于所有支持语音的平台（QQ、企业微信等）
6. **写死响应**: 系统响应（如"会话已重置"）自动跳过语音转换
7. **发送顺序**: voice_with_text=0 时确保只发语音不发文字
