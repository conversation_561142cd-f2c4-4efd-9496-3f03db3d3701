# Windows ffmpeg 安装指南

## 方法一：手动安装（推荐）

### 1. 下载 ffmpeg
1. 访问 https://www.gyan.dev/ffmpeg/builds/
2. 点击 "release builds"
3. 下载 "ffmpeg-release-essentials.zip"

### 2. 解压文件
1. 将下载的 zip 文件解压到 `C:\ffmpeg`
2. 确保目录结构为：
   ```
   C:\ffmpeg\
   ├── bin\
   │   ├── ffmpeg.exe
   │   ├── ffplay.exe
   │   └── ffprobe.exe
   ├── doc\
   └── presets\
   ```

### 3. 添加到环境变量
1. 右键点击"此电脑" → "属性"
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"系统变量"中找到"Path"，点击"编辑"
5. 点击"新建"，添加：`C:\ffmpeg\bin`
6. 点击"确定"保存所有设置

### 4. 验证安装
1. 打开命令提示符（cmd）
2. 输入：`ffmpeg -version`
3. 如果显示版本信息，说明安装成功

## 方法二：使用包管理器

### 使用 Chocolatey
```bash
# 安装 Chocolatey（如果没有）
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装 ffmpeg
choco install ffmpeg
```

### 使用 Scoop
```bash
# 安装 Scoop（如果没有）
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
irm get.scoop.sh | iex

# 安装 ffmpeg
scoop install ffmpeg
```

## 常见问题

### 问题1：命令提示符找不到 ffmpeg
**解决方案**：
1. 重新启动命令提示符
2. 检查环境变量是否正确添加
3. 确保路径是 `C:\ffmpeg\bin` 而不是 `C:\ffmpeg`

### 问题2：权限问题
**解决方案**：
1. 以管理员身份运行命令提示符
2. 确保有写入 C:\ 目录的权限

### 问题3：Python 仍然找不到 ffmpeg
**解决方案**：
1. 重启 Python 程序
2. 重启整个开发环境
3. 检查 Python 环境变量

## 验证 pydub 是否能使用 ffmpeg

创建测试脚本：
```python
from pydub import AudioSegment
import tempfile

# 测试音频转换
try:
    # 创建一个简单的音频
    audio = AudioSegment.silent(duration=1000)  # 1秒静音
    
    # 尝试导出为不同格式
    with tempfile.NamedTemporaryFile(suffix=".wav") as wav_file:
        audio.export(wav_file.name, format="wav")
        print("✅ WAV 导出成功")
    
    with tempfile.NamedTemporaryFile(suffix=".mp3") as mp3_file:
        audio.export(mp3_file.name, format="mp3")
        print("✅ MP3 导出成功")
        
    with tempfile.NamedTemporaryFile(suffix=".amr") as amr_file:
        audio.export(amr_file.name, format="amr")
        print("✅ AMR 导出成功")
        
    print("🎉 ffmpeg 配置成功！")
    
except Exception as e:
    print(f"❌ ffmpeg 配置失败: {e}")
```

## 安装完成后

安装完成后，你的 GPT-SoVITS 语音功能就能正常转换为 AMR 格式了！
