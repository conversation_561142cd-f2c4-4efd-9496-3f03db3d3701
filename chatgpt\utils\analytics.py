#!/usr/bin/env python3
"""
埋点数据收集和分析模块
"""

import asyncio
import json
import time
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from pathlib import Path
from loguru import logger
from pymongo import MongoClient, ASCENDING
from pymongo.errors import DuplicateKeyError, ConnectionFailure


class EventType(Enum):
    """事件类型枚举"""
    # 语音相关事件
    VOICE_SUCCESS = "voice_success"
    VOICE_FALLBACK = "voice_fallback"
    VOICE_ERROR = "voice_error"
    VOICE_REQUEST = "voice_request"
    VOICE_STRATEGY_TRIGGER = "voice_strategy_trigger"
    VOICE_STRATEGY_SKIP = "voice_strategy_skip"
    
    # 系统事件
    RESET_SESSION = "reset_session"
    SWITCH_AI = "switch_ai"
    DRAW_REQUEST = "draw_request"
    ERROR_OCCURRED = "error_occurred"
    MESSAGE_SENT = "message_sent"
    CONVERSATION_START = "conversation_start"
    CONVERSATION_END = "conversation_end"


@dataclass
class AnalyticsEvent:
    """埋点事件数据结构"""
    user_id: str
    platform: str
    event_type: str
    event_detail: Optional[str] = None
    session_id: Optional[str] = None
    processing_time_ms: Optional[int] = None
    voice_mode: Optional[str] = None
    error_type: Optional[str] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class AnalyticsManager:
    """埋点数据管理器"""

    def __init__(self, mongo_uri: str = "mongodb://localhost:27017/", db_name: str = "lovebot_analytics"):
        self.mongo_uri = mongo_uri
        self.db_name = db_name
        self.client = None
        self.db = None
        self.lock = threading.Lock()
        self.event_queue = asyncio.Queue()
        self.init_database()

        # 启动后台处理任务
        asyncio.create_task(self._process_events())
        
    def init_database(self):
        """初始化MongoDB数据库和集合"""
        try:
            # 连接MongoDB
            self.client = MongoClient(self.mongo_uri, serverSelectionTimeoutMS=5000)
            self.db = self.client[self.db_name]

            # 测试连接
            self.client.admin.command('ping')

            # 创建集合和索引
            collections = {
                'user_activity': [
                    [('user_id', ASCENDING), ('activity_date', ASCENDING)],  # 复合唯一索引
                    [('activity_date', ASCENDING)],
                    [('platform', ASCENDING)]
                ],
                'voice_stats': [
                    [('user_id', ASCENDING), ('created_at', ASCENDING)],
                    [('event_type', ASCENDING), ('created_at', ASCENDING)],
                    [('platform', ASCENDING), ('created_at', ASCENDING)]
                ],
                'system_events': [
                    [('user_id', ASCENDING), ('created_at', ASCENDING)],
                    [('event_type', ASCENDING), ('created_at', ASCENDING)],
                    [('session_id', ASCENDING)]
                ],
                'daily_summary': [
                    [('summary_date', ASCENDING), ('platform', ASCENDING)],  # 复合唯一索引
                    [('summary_date', ASCENDING)]
                ]
            }

            for collection_name, indexes in collections.items():
                collection = self.db[collection_name]

                # 创建索引
                for index_fields in indexes:
                    try:
                        if collection_name in ['user_activity', 'daily_summary'] and len(index_fields) == 2:
                            # 为复合字段创建唯一索引
                            collection.create_index(index_fields, unique=True, background=True)
                        else:
                            collection.create_index(index_fields, background=True)
                    except DuplicateKeyError:
                        pass  # 索引已存在

            logger.info(f"MongoDB埋点数据库初始化完成: {self.mongo_uri}/{self.db_name}")

        except ConnectionFailure as e:
            logger.error(f"MongoDB连接失败: {e}")
            logger.warning("将使用内存模式运行（数据不会持久化）")
            self.client = None
            self.db = None
        except Exception as e:
            logger.error(f"MongoDB初始化失败: {e}")
            self.client = None
            self.db = None
    
    async def track_event(self, event: AnalyticsEvent):
        """记录埋点事件"""
        try:
            await self.event_queue.put(event)
            logger.debug(f"埋点事件已加入队列: {event.event_type} - {event.user_id}")
        except Exception as e:
            logger.error(f"埋点事件记录失败: {e}")
    
    async def _process_events(self):
        """后台处理埋点事件"""
        while True:
            try:
                # 批量处理事件
                events = []
                try:
                    # 等待第一个事件
                    event = await asyncio.wait_for(self.event_queue.get(), timeout=5.0)
                    events.append(event)
                    
                    # 收集更多事件（最多等待1秒）
                    start_time = time.time()
                    while len(events) < 100 and (time.time() - start_time) < 1.0:
                        try:
                            event = await asyncio.wait_for(self.event_queue.get(), timeout=0.1)
                            events.append(event)
                        except asyncio.TimeoutError:
                            break
                            
                except asyncio.TimeoutError:
                    continue
                
                if events:
                    await self._batch_insert_events(events)
                    
            except Exception as e:
                logger.error(f"埋点事件处理异常: {e}")
                await asyncio.sleep(1)
    
    async def _batch_insert_events(self, events: List[AnalyticsEvent]):
        """批量插入事件数据"""
        if not self.db:
            logger.warning("MongoDB未连接，跳过数据插入")
            return

        try:
            for event in events:
                try:
                    # 更新用户活跃度
                    await self._update_user_activity_mongo(event)

                    # 插入具体事件
                    if event.event_type.startswith('voice_'):
                        await self._insert_voice_event_mongo(event)
                    else:
                        await self._insert_system_event_mongo(event)

                except Exception as e:
                    logger.error(f"插入埋点事件失败: {e}, 事件: {event}")

            logger.debug(f"批量插入 {len(events)} 个埋点事件到MongoDB")

        except Exception as e:
            logger.error(f"MongoDB批量插入失败: {e}")
    
    async def _update_user_activity_mongo(self, event: AnalyticsEvent):
        """更新用户活跃度数据"""
        today = date.today()

        # 检查是否为新用户
        existing_user = self.db.user_activity.find_one({"user_id": event.user_id})
        is_new_user = existing_user is None

        # 准备更新数据
        update_data = {
            "$set": {
                "user_id": event.user_id,
                "platform": event.platform,
                "activity_date": today,
                "last_active_time": event.timestamp,
                "updated_at": datetime.now()
            },
            "$inc": {}
        }

        # 增加消息计数
        if event.event_type == 'message_sent':
            update_data["$inc"]["message_count"] = 1

        # 增加对话轮数
        if event.event_type == 'conversation_start':
            update_data["$inc"]["conversation_rounds"] = 1

        # 设置首次见到日期
        if is_new_user:
            update_data["$setOnInsert"] = {"first_seen_date": today}

        # 使用upsert更新或插入
        self.db.user_activity.update_one(
            {"user_id": event.user_id, "activity_date": today},
            update_data,
            upsert=True
        )

    async def _insert_voice_event_mongo(self, event: AnalyticsEvent):
        """插入语音相关事件"""
        voice_doc = {
            "user_id": event.user_id,
            "platform": event.platform,
            "event_type": event.event_type,
            "event_detail": event.event_detail,
            "voice_mode": event.voice_mode,
            "error_type": event.error_type,
            "processing_time_ms": event.processing_time_ms,
            "created_at": event.timestamp
        }
        self.db.voice_stats.insert_one(voice_doc)

    async def _insert_system_event_mongo(self, event: AnalyticsEvent):
        """插入系统事件"""
        system_doc = {
            "user_id": event.user_id,
            "platform": event.platform,
            "event_type": event.event_type,
            "event_detail": event.event_detail,
            "session_id": event.session_id,
            "processing_time_ms": event.processing_time_ms,
            "created_at": event.timestamp
        }
        self.db.system_events.insert_one(system_doc)


# 全局埋点管理器实例
# 可以通过环境变量配置MongoDB连接
import os
mongo_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
db_name = os.getenv('ANALYTICS_DB_NAME', 'lovebot_analytics')
analytics_manager = AnalyticsManager(mongo_uri, db_name)


# 便捷函数
async def track_voice_success(user_id: str, platform: str, voice_mode: str, processing_time_ms: int = None):
    """记录语音成功事件"""
    event = AnalyticsEvent(
        user_id=user_id,
        platform=platform,
        event_type=EventType.VOICE_SUCCESS.value,
        voice_mode=voice_mode,
        processing_time_ms=processing_time_ms
    )
    await analytics_manager.track_event(event)


async def track_voice_fallback(user_id: str, platform: str, reason: str):
    """记录语音回退事件"""
    event = AnalyticsEvent(
        user_id=user_id,
        platform=platform,
        event_type=EventType.VOICE_FALLBACK.value,
        event_detail=reason
    )
    await analytics_manager.track_event(event)


async def track_voice_error(user_id: str, platform: str, error_type: str, error_detail: str):
    """记录语音错误事件"""
    event = AnalyticsEvent(
        user_id=user_id,
        platform=platform,
        event_type=EventType.VOICE_ERROR.value,
        event_detail=error_detail,
        error_type=error_type
    )
    await analytics_manager.track_event(event)


async def track_system_event(user_id: str, platform: str, event_type: str, 
                           event_detail: str = None, session_id: str = None, 
                           processing_time_ms: int = None):
    """记录系统事件"""
    event = AnalyticsEvent(
        user_id=user_id,
        platform=platform,
        event_type=event_type,
        event_detail=event_detail,
        session_id=session_id,
        processing_time_ms=processing_time_ms
    )
    await analytics_manager.track_event(event)
