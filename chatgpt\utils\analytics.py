#!/usr/bin/env python3
"""
埋点数据收集和分析模块
"""

import asyncio
import json
import time
from datetime import datetime, date, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
import sqlite3
import threading
from pathlib import Path
from loguru import logger


class EventType(Enum):
    """事件类型枚举"""
    # 语音相关事件
    VOICE_SUCCESS = "voice_success"
    VOICE_FALLBACK = "voice_fallback"
    VOICE_ERROR = "voice_error"
    VOICE_REQUEST = "voice_request"
    VOICE_STRATEGY_TRIGGER = "voice_strategy_trigger"
    VOICE_STRATEGY_SKIP = "voice_strategy_skip"
    
    # 系统事件
    RESET_SESSION = "reset_session"
    SWITCH_AI = "switch_ai"
    DRAW_REQUEST = "draw_request"
    ERROR_OCCURRED = "error_occurred"
    MESSAGE_SENT = "message_sent"
    CONVERSATION_START = "conversation_start"
    CONVERSATION_END = "conversation_end"


@dataclass
class AnalyticsEvent:
    """埋点事件数据结构"""
    user_id: str
    platform: str
    event_type: str
    event_detail: Optional[str] = None
    session_id: Optional[str] = None
    processing_time_ms: Optional[int] = None
    voice_mode: Optional[str] = None
    error_type: Optional[str] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class AnalyticsManager:
    """埋点数据管理器"""
    
    def __init__(self, db_path: str = "analytics.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self.event_queue = asyncio.Queue()
        self.init_database()
        
        # 启动后台处理任务
        asyncio.create_task(self._process_events())
        
    def init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 用户活跃度表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_activity (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    platform TEXT NOT NULL,
                    activity_date DATE NOT NULL,
                    message_count INTEGER DEFAULT 0,
                    conversation_rounds INTEGER DEFAULT 0,
                    first_seen_date DATE,
                    last_active_time DATETIME,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, activity_date)
                )
            """)
            
            # 语音统计表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS voice_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    platform TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    event_detail TEXT,
                    voice_mode TEXT,
                    error_type TEXT,
                    processing_time_ms INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 系统事件表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    platform TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    event_detail TEXT,
                    session_id TEXT,
                    processing_time_ms INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 每日统计汇总表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS daily_summary (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    summary_date DATE NOT NULL,
                    platform TEXT NOT NULL,
                    active_users INTEGER DEFAULT 0,
                    new_users INTEGER DEFAULT 0,
                    total_messages INTEGER DEFAULT 0,
                    total_conversations INTEGER DEFAULT 0,
                    voice_success_rate REAL,
                    avg_response_time_ms INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(summary_date, platform)
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_activity_date ON user_activity(user_id, activity_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_voice_stats_date ON voice_stats(user_id, created_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_system_events_date ON system_events(user_id, created_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_voice_event_type ON voice_stats(event_type, created_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_system_event_type ON system_events(event_type, created_at)")
            
            conn.commit()
            logger.info("埋点数据库初始化完成")
    
    async def track_event(self, event: AnalyticsEvent):
        """记录埋点事件"""
        try:
            await self.event_queue.put(event)
            logger.debug(f"埋点事件已加入队列: {event.event_type} - {event.user_id}")
        except Exception as e:
            logger.error(f"埋点事件记录失败: {e}")
    
    async def _process_events(self):
        """后台处理埋点事件"""
        while True:
            try:
                # 批量处理事件
                events = []
                try:
                    # 等待第一个事件
                    event = await asyncio.wait_for(self.event_queue.get(), timeout=5.0)
                    events.append(event)
                    
                    # 收集更多事件（最多等待1秒）
                    start_time = time.time()
                    while len(events) < 100 and (time.time() - start_time) < 1.0:
                        try:
                            event = await asyncio.wait_for(self.event_queue.get(), timeout=0.1)
                            events.append(event)
                        except asyncio.TimeoutError:
                            break
                            
                except asyncio.TimeoutError:
                    continue
                
                if events:
                    await self._batch_insert_events(events)
                    
            except Exception as e:
                logger.error(f"埋点事件处理异常: {e}")
                await asyncio.sleep(1)
    
    async def _batch_insert_events(self, events: List[AnalyticsEvent]):
        """批量插入事件数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            for event in events:
                try:
                    # 更新用户活跃度
                    await self._update_user_activity(cursor, event)
                    
                    # 插入具体事件
                    if event.event_type.startswith('voice_'):
                        await self._insert_voice_event(cursor, event)
                    else:
                        await self._insert_system_event(cursor, event)
                        
                except Exception as e:
                    logger.error(f"插入埋点事件失败: {e}, 事件: {event}")
            
            conn.commit()
            logger.debug(f"批量插入 {len(events)} 个埋点事件")
    
    async def _update_user_activity(self, cursor, event: AnalyticsEvent):
        """更新用户活跃度数据"""
        today = date.today()
        
        # 检查是否为新用户
        cursor.execute("""
            SELECT first_seen_date FROM user_activity 
            WHERE user_id = ? LIMIT 1
        """, (event.user_id,))
        
        is_new_user = cursor.fetchone() is None
        
        # 更新或插入用户活跃度记录
        cursor.execute("""
            INSERT OR REPLACE INTO user_activity 
            (user_id, platform, activity_date, message_count, conversation_rounds, 
             first_seen_date, last_active_time, updated_at)
            VALUES (
                ?, ?, ?, 
                COALESCE((SELECT message_count FROM user_activity WHERE user_id = ? AND activity_date = ?), 0) + 
                    CASE WHEN ? = 'message_sent' THEN 1 ELSE 0 END,
                COALESCE((SELECT conversation_rounds FROM user_activity WHERE user_id = ? AND activity_date = ?), 0) + 
                    CASE WHEN ? = 'conversation_start' THEN 1 ELSE 0 END,
                COALESCE((SELECT first_seen_date FROM user_activity WHERE user_id = ? LIMIT 1), ?),
                ?, 
                CURRENT_TIMESTAMP
            )
        """, (
            event.user_id, event.platform, today,
            event.user_id, today, event.event_type,
            event.user_id, today, event.event_type,
            event.user_id, today if is_new_user else None,
            event.timestamp
        ))
    
    async def _insert_voice_event(self, cursor, event: AnalyticsEvent):
        """插入语音相关事件"""
        cursor.execute("""
            INSERT INTO voice_stats 
            (user_id, platform, event_type, event_detail, voice_mode, error_type, processing_time_ms, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            event.user_id, event.platform, event.event_type, event.event_detail,
            event.voice_mode, event.error_type, event.processing_time_ms, event.timestamp
        ))
    
    async def _insert_system_event(self, cursor, event: AnalyticsEvent):
        """插入系统事件"""
        cursor.execute("""
            INSERT INTO system_events 
            (user_id, platform, event_type, event_detail, session_id, processing_time_ms, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            event.user_id, event.platform, event.event_type, event.event_detail,
            event.session_id, event.processing_time_ms, event.timestamp
        ))


# 全局埋点管理器实例
analytics_manager = AnalyticsManager()


# 便捷函数
async def track_voice_success(user_id: str, platform: str, voice_mode: str, processing_time_ms: int = None):
    """记录语音成功事件"""
    event = AnalyticsEvent(
        user_id=user_id,
        platform=platform,
        event_type=EventType.VOICE_SUCCESS.value,
        voice_mode=voice_mode,
        processing_time_ms=processing_time_ms
    )
    await analytics_manager.track_event(event)


async def track_voice_fallback(user_id: str, platform: str, reason: str):
    """记录语音回退事件"""
    event = AnalyticsEvent(
        user_id=user_id,
        platform=platform,
        event_type=EventType.VOICE_FALLBACK.value,
        event_detail=reason
    )
    await analytics_manager.track_event(event)


async def track_voice_error(user_id: str, platform: str, error_type: str, error_detail: str):
    """记录语音错误事件"""
    event = AnalyticsEvent(
        user_id=user_id,
        platform=platform,
        event_type=EventType.VOICE_ERROR.value,
        event_detail=error_detail,
        error_type=error_type
    )
    await analytics_manager.track_event(event)


async def track_system_event(user_id: str, platform: str, event_type: str, 
                           event_detail: str = None, session_id: str = None, 
                           processing_time_ms: int = None):
    """记录系统事件"""
    event = AnalyticsEvent(
        user_id=user_id,
        platform=platform,
        event_type=event_type,
        event_detail=event_detail,
        session_id=session_id,
        processing_time_ms=processing_time_ms
    )
    await analytics_manager.track_event(event)
