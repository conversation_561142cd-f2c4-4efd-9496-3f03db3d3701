#!/usr/bin/env python3
"""
埋点数据分析和报告模块
"""

import sqlite3
from datetime import datetime, date, timedelta
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass
import json
from loguru import logger


@dataclass
class DailyReport:
    """每日报告数据结构"""
    date: str
    platform: str
    active_users: int
    new_users: int
    total_messages: int
    total_conversations: int
    voice_success_rate: float
    avg_response_time_ms: int
    peak_hours: List[int]
    top_errors: List[Dict[str, Any]]


class AnalyticsReporter:
    """埋点数据分析报告器"""
    
    def __init__(self, db_path: str = "analytics.db"):
        self.db_path = db_path
    
    def get_daily_active_users(self, target_date: date = None, platform: str = None) -> int:
        """获取单日活跃用户数"""
        if target_date is None:
            target_date = date.today()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT COUNT(DISTINCT user_id) 
                FROM user_activity 
                WHERE activity_date = ?
            """
            params = [target_date]
            
            if platform:
                query += " AND platform = ?"
                params.append(platform)
            
            cursor.execute(query, params)
            result = cursor.fetchone()
            return result[0] if result else 0
    
    def get_new_users_count(self, target_date: date = None, platform: str = None) -> int:
        """获取每日新添加用户数"""
        if target_date is None:
            target_date = date.today()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT COUNT(DISTINCT user_id) 
                FROM user_activity 
                WHERE first_seen_date = ?
            """
            params = [target_date]
            
            if platform:
                query += " AND platform = ?"
                params.append(platform)
            
            cursor.execute(query, params)
            result = cursor.fetchone()
            return result[0] if result else 0
    
    def get_peak_hours(self, target_date: date = None, platform: str = None) -> List[Tuple[int, int]]:
        """获取用户高频在线时间段"""
        if target_date is None:
            target_date = date.today()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT 
                    CAST(strftime('%H', last_active_time) AS INTEGER) as hour,
                    COUNT(DISTINCT user_id) as user_count
                FROM user_activity 
                WHERE activity_date = ?
            """
            params = [target_date]
            
            if platform:
                query += " AND platform = ?"
                params.append(platform)
            
            query += """
                GROUP BY hour 
                ORDER BY user_count DESC, hour
            """
            
            cursor.execute(query, params)
            return cursor.fetchall()
    
    def get_conversation_stats(self, target_date: date = None, platform: str = None) -> Dict[str, float]:
        """获取对话统计数据"""
        if target_date is None:
            target_date = date.today()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT 
                    AVG(conversation_rounds) as avg_rounds,
                    MAX(conversation_rounds) as max_rounds,
                    SUM(conversation_rounds) as total_rounds,
                    COUNT(DISTINCT user_id) as active_users
                FROM user_activity 
                WHERE activity_date = ?
            """
            params = [target_date]
            
            if platform:
                query += " AND platform = ?"
                params.append(platform)
            
            cursor.execute(query, params)
            result = cursor.fetchone()
            
            if result and result[0] is not None:
                return {
                    'avg_conversation_rounds': round(result[0], 2),
                    'max_conversation_rounds': result[1],
                    'total_conversations': result[2],
                    'active_users': result[3]
                }
            return {
                'avg_conversation_rounds': 0,
                'max_conversation_rounds': 0,
                'total_conversations': 0,
                'active_users': 0
            }
    
    def get_reset_frequency(self, days: int = 7, platform: str = None) -> Dict[str, Any]:
        """获取重置会话命令使用频率"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT 
                    DATE(created_at) as event_date,
                    COUNT(*) as reset_count,
                    COUNT(DISTINCT user_id) as unique_users
                FROM system_events 
                WHERE event_type = 'reset_session' 
                AND DATE(created_at) BETWEEN ? AND ?
            """
            params = [start_date, end_date]
            
            if platform:
                query += " AND platform = ?"
                params.append(platform)
            
            query += " GROUP BY DATE(created_at) ORDER BY event_date"
            
            cursor.execute(query, params)
            daily_resets = cursor.fetchall()
            
            # 总体统计
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_resets,
                    COUNT(DISTINCT user_id) as unique_users,
                    AVG(CAST(COUNT(*) AS FLOAT)) as avg_per_day
                FROM system_events 
                WHERE event_type = 'reset_session' 
                AND DATE(created_at) BETWEEN ? AND ?
                """ + (" AND platform = ?" if platform else ""), 
                params
            )
            total_stats = cursor.fetchone()
            
            return {
                'period_days': days,
                'daily_resets': [{'date': row[0], 'count': row[1], 'unique_users': row[2]} for row in daily_resets],
                'total_resets': total_stats[0] if total_stats else 0,
                'unique_users': total_stats[1] if total_stats else 0,
                'avg_resets_per_day': round(total_stats[2], 2) if total_stats and total_stats[2] else 0
            }
    
    def get_voice_stats(self, target_date: date = None, platform: str = None) -> Dict[str, Any]:
        """获取语音功能统计"""
        if target_date is None:
            target_date = date.today()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 语音成功率
            query = """
                SELECT 
                    event_type,
                    COUNT(*) as count
                FROM voice_stats 
                WHERE DATE(created_at) = ?
            """
            params = [target_date]
            
            if platform:
                query += " AND platform = ?"
                params.append(platform)
            
            query += " GROUP BY event_type"
            
            cursor.execute(query, params)
            voice_events = dict(cursor.fetchall())
            
            total_voice_attempts = sum(voice_events.values())
            success_count = voice_events.get('voice_success', 0)
            fallback_count = voice_events.get('voice_fallback', 0)
            error_count = voice_events.get('voice_error', 0)
            
            success_rate = (success_count / total_voice_attempts * 100) if total_voice_attempts > 0 else 0
            
            # 语音模式分布
            cursor.execute("""
                SELECT 
                    voice_mode,
                    COUNT(*) as count
                FROM voice_stats 
                WHERE DATE(created_at) = ? AND event_type = 'voice_success'
                """ + (" AND platform = ?" if platform else "") + """
                GROUP BY voice_mode
            """, params)
            voice_modes = dict(cursor.fetchall())
            
            # 语音错误类型
            cursor.execute("""
                SELECT 
                    error_type,
                    COUNT(*) as count
                FROM voice_stats 
                WHERE DATE(created_at) = ? AND event_type = 'voice_error'
                """ + (" AND platform = ?" if platform else "") + """
                GROUP BY error_type
                ORDER BY count DESC
                LIMIT 5
            """, params)
            top_errors = [{'error_type': row[0], 'count': row[1]} for row in cursor.fetchall()]
            
            return {
                'total_attempts': total_voice_attempts,
                'success_count': success_count,
                'fallback_count': fallback_count,
                'error_count': error_count,
                'success_rate': round(success_rate, 2),
                'voice_modes': voice_modes,
                'top_errors': top_errors
            }
    
    def get_ai_switch_stats(self, days: int = 7, platform: str = None) -> Dict[str, Any]:
        """获取AI切换统计"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = """
                SELECT 
                    event_detail,
                    COUNT(*) as count,
                    COUNT(DISTINCT user_id) as unique_users
                FROM system_events 
                WHERE event_type = 'switch_ai' 
                AND DATE(created_at) BETWEEN ? AND ?
            """
            params = [start_date, end_date]
            
            if platform:
                query += " AND platform = ?"
                params.append(platform)
            
            query += " GROUP BY event_detail ORDER BY count DESC"
            
            cursor.execute(query, params)
            ai_switches = [{'ai_type': row[0], 'count': row[1], 'unique_users': row[2]} for row in cursor.fetchall()]
            
            return {
                'period_days': days,
                'ai_switches': ai_switches,
                'total_switches': sum(item['count'] for item in ai_switches)
            }
    
    def generate_daily_report(self, target_date: date = None, platform: str = None) -> DailyReport:
        """生成每日报告"""
        if target_date is None:
            target_date = date.today()
        
        # 收集各项数据
        active_users = self.get_daily_active_users(target_date, platform)
        new_users = self.get_new_users_count(target_date, platform)
        conversation_stats = self.get_conversation_stats(target_date, platform)
        voice_stats = self.get_voice_stats(target_date, platform)
        peak_hours_data = self.get_peak_hours(target_date, platform)
        
        # 提取峰值时间段（前3个小时）
        peak_hours = [hour for hour, count in peak_hours_data[:3]]
        
        return DailyReport(
            date=target_date.isoformat(),
            platform=platform or "all",
            active_users=active_users,
            new_users=new_users,
            total_messages=conversation_stats.get('total_conversations', 0),
            total_conversations=conversation_stats.get('total_conversations', 0),
            voice_success_rate=voice_stats.get('success_rate', 0),
            avg_response_time_ms=0,  # TODO: 实现响应时间统计
            peak_hours=peak_hours,
            top_errors=voice_stats.get('top_errors', [])
        )
    
    def export_report_json(self, report: DailyReport, file_path: str = None) -> str:
        """导出报告为JSON格式"""
        if file_path is None:
            file_path = f"daily_report_{report.date}_{report.platform}.json"
        
        report_dict = {
            'report_date': report.date,
            'platform': report.platform,
            'user_metrics': {
                'active_users': report.active_users,
                'new_users': report.new_users,
                'peak_hours': report.peak_hours
            },
            'conversation_metrics': {
                'total_messages': report.total_messages,
                'total_conversations': report.total_conversations
            },
            'voice_metrics': {
                'success_rate': report.voice_success_rate,
                'top_errors': report.top_errors
            },
            'performance_metrics': {
                'avg_response_time_ms': report.avg_response_time_ms
            }
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, ensure_ascii=False, indent=2)
        
        logger.info(f"报告已导出到: {file_path}")
        return file_path


# 全局报告器实例
analytics_reporter = AnalyticsReporter()
