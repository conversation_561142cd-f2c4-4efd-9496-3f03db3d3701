import asyncio
import re
from typing import Callable
import json
import os
import httpcore
import httpx
import openai
from graia.ariadne.message.chain import Message<PERSON>hai<PERSON>
from graia.ariadne.message.element import Plain
from httpx import ConnectTimeout
from loguru import logger
from requests.exceptions import SS<PERSON>rror, ProxyError, RequestException
from urllib3.exceptions import MaxRetryError
import pandas as pd
import requests
import random
from multiprocessing import Lock
import datetime

from constants import botManager, BotPlatform
from constants import config
from conversation import ConversationHandler, ConversationContext
from exceptions import PresetNotFoundException, BotRatelimitException, ConcurrentMessageException, \
    BotTypeNotFoundException, NoAvailableBotException, BotOperationNotSupportedException, CommandRefusedException, \
    DrawingFailedException
from middlewares.baiducloud import MiddlewareBaiduCloud
from middlewares.concurrentlock import MiddlewareConcurrentLock
from middlewares.ratelimit import MiddlewareRatelimit
from middlewares.timeout import MiddlewareTimeout
from utils.text_to_speech import get_tts_voice, Tts<PERSON><PERSON>ceManager, VoiceType
from pymongo import MongoClient
from manager.info_manage import InfoManager
from manager.ratelimit import RateLimitManager

from log_config import setup_logger
logger = setup_logger()


middlewares = [MiddlewareTimeout(), MiddlewareRatelimit(), MiddlewareBaiduCloud(), MiddlewareConcurrentLock()]
manager_r = RateLimitManager()
manager_i = InfoManager()

# 创建全局锁
lock = Lock()

# 语音请求状态管理
voice_request_sessions = {}  # 存储每个会话的语音请求状态


def is_hardcoded_response(msg: MessageChain) -> bool:
    """
    检测是否为写死的系统响应，这些响应不应该转换为语音

    Args:
        msg: MessageChain 对象

    Returns:
        bool: 是否为写死响应
    """
    # 提取消息文本
    text_content = ""
    for elem in msg:
        if isinstance(elem, Plain):
            text_content += str(elem).strip()

    if not text_content:
        return False

    # 定义写死响应的关键词列表
    hardcoded_responses = [
        # [response] 部分的响应
        config.response.reset,  # "会话已重置。"
        config.response.rollback_success,  # "【系统消息】已回滚至上一条对话，你刚刚发的我就忘记啦！"
        config.response.rollback_fail.replace("{reset}", "重置会话"),  # 回滚失败消息
        config.response.placeholder,  # 占位符消息
        config.response.timeout_format,  # 超时提醒
        config.response.cancel_wait_too_long,  # 等待过长取消消息
        config.response.queue_full,  # 队列满提示
        config.response.queued_notice,  # 队列通知
        config.response.error_format.split("{exc}")[0].strip(),  # 错误格式前半部分
        config.response.error_network_failure.split("{exc}")[0].strip(),  # 网络错误前半部分
        config.response.error_session_authenciate_failed.split("{exc}")[0].strip(),  # 认证失败前半部分
        config.response.error_request_too_many.split("{exc}")[0].strip(),  # 请求过多前半部分
        config.response.error_request_concurrent_error,  # 并发错误
        config.response.error_server_overloaded,  # 服务器过载
        config.response.error_drawing.split("{exc}")[0].strip(),  # 画图失败前半部分

        # [ratelimit] 部分的响应
        config.ratelimit.exceed,  # 超额消息
        config.ratelimit.exceed_date,  # 超期消息
        config.ratelimit.draw_exceed,  # 画图超额

        # 其他常见的系统响应
        "已切换至",  # AI切换响应
        "语音已启用",  # 语音启用响应
        "已关闭语音",  # 语音关闭响应
        "已切换至图文混合模式",  # 模式切换
        "已切换至纯图片模式",
        "已切换至纯文字模式",
        "【系统消息】",  # 系统消息前缀
        "【系统提示】",  # 系统提示前缀
        "不好意思，只有管理员才能切换AI！",
        "未配置",  # 配置相关错误
        "您没有权限执行这个操作",
        "配置文件重新载入完毕！",
        "重新登录账号中",
        "登录结束",
        "额度更新成功！",
        "预设加载成功！",
        "提供的语音ID无效",
        "模型不存在！",
    ]

    # 检查是否匹配任何写死响应
    for response_pattern in hardcoded_responses:
        if response_pattern and response_pattern in text_content:
            logger.debug(f"[写死响应检测] 检测到写死响应: '{text_content[:50]}...' 匹配模式: '{response_pattern}'")
            return True

    # 检查是否包含特定的系统响应模式
    system_patterns = [
        r"【系统.*?】",  # 系统消息模式
        # r"已切换至.*?模式",  # 模式切换模式
        # r".*?已启用",  # 功能启用模式
        # r".*?已关闭",  # 功能关闭模式
        r"额度.*?成功",  # 额度操作成功
        # r".*?不存在.*?",  # 不存在模式
        # r".*?失败.*?",  # 失败模式
    ]

    for pattern in system_patterns:
        if re.search(pattern, text_content):
            logger.debug(f"[写死响应检测] 检测到系统响应模式: '{text_content[:50]}...' 匹配模式: '{pattern}'")
            return True

    return False


def is_voice_request(msg: MessageChain) -> bool:
    """
    检测用户是否明确要求语音回复

    Args:
        msg: MessageChain 对象

    Returns:
        bool: 是否为语音请求
    """
    if not config.text_to_speech.voice_request_enabled:
        return False

    # 提取消息文本
    text_content = ""
    for elem in msg:
        if isinstance(elem, Plain):
            text_content += str(elem).strip()

    if not text_content:
        return False

    # 转换为小写便于匹配
    text_lower = text_content.lower()

    # 定义语音请求关键词
    voice_request_keywords = [
        "语音回复", "用语音回复", "语音说", "语音告诉我",
        "你可以语音回复我吗", "能语音回复吗", "可以语音回复吗",
        "说话", "用声音说", "用声音回复",
        "想听你的声音", "想听语音", "想听你说话",
        "语音聊天", "用语音聊", "语音对话",
        "开启语音", "启用语音", "打开语音",
    ]

    # 检查是否包含语音请求关键词
    for keyword in voice_request_keywords:
        if keyword in text_lower:
            logger.debug(f"[语音请求] 检测到语音请求关键词: '{keyword}' 在消息: '{text_content[:30]}...'")
            return True

    return False


def is_voice_close_request(msg: MessageChain) -> bool:
    """
    检测用户是否要求关闭语音

    Args:
        msg: MessageChain 对象

    Returns:
        bool: 是否为关闭语音请求
    """
    # 提取消息文本
    text_content = ""
    for elem in msg:
        if isinstance(elem, Plain):
            text_content += str(elem).strip()

    if not text_content:
        return False

    # 转换为小写便于匹配
    text_lower = text_content.lower()

    # 定义关闭语音关键词
    close_keywords = [
        "关闭语音", "停止语音", "取消语音",
        "不要语音", "别语音", "关掉语音",
        "退出语音", "结束语音", "语音关闭",
    ]

    # 检查是否包含关闭语音关键词
    for keyword in close_keywords:
        if keyword in text_lower:
            logger.debug(f"[语音请求] 检测到关闭语音关键词: '{keyword}' 在消息: '{text_content[:30]}...'")
            return True

    return False


def get_voice_request_status(session_id: str) -> dict:
    """
    获取会话的语音请求状态

    Args:
        session_id: 会话ID

    Returns:
        dict: 语音请求状态信息
    """
    return voice_request_sessions.get(session_id, {
        "active": False,
        "remaining_count": 0,
        "total_count": 0
    })


def set_voice_request_status(session_id: str, active: bool, count: int = None):
    """
    设置会话的语音请求状态

    Args:
        session_id: 会话ID
        active: 是否激活语音请求模式
        count: 剩余回复条数
    """
    if active:
        if count is None:
            count = config.text_to_speech.voice_request_count
        voice_request_sessions[session_id] = {
            "active": True,
            "remaining_count": count,
            "total_count": count
        }
        logger.info(f"[语音请求] 会话 {session_id} 启用语音请求模式，剩余 {count} 条")
    else:
        if session_id in voice_request_sessions:
            del voice_request_sessions[session_id]
        logger.info(f"[语音请求] 会话 {session_id} 关闭语音请求模式")


def update_voice_request_count(session_id: str) -> bool:
    """
    更新语音请求计数，返回是否还需要继续语音模式

    Args:
        session_id: 会话ID

    Returns:
        bool: 是否还需要继续语音模式
    """
    if session_id not in voice_request_sessions:
        return False

    status = voice_request_sessions[session_id]
    status["remaining_count"] -= 1

    logger.debug(f"[语音请求] 会话 {session_id} 剩余语音回复: {status['remaining_count']}")

    if status["remaining_count"] <= 0:
        set_voice_request_status(session_id, False)
        return False

    return True


def should_trigger_voice_strategy(msg: MessageChain) -> bool:
    """
    语音策略V0：根据字符数量和概率决定是否触发语音

    Args:
        msg: MessageChain 对象

    Returns:
        bool: 是否应该发送语音
    """
    # 检查是否启用语音策略
    if not config.text_to_speech.voice_strategy_enabled:
        logger.debug("[语音策略V0] 语音策略已禁用，使用原始逻辑")
        return True  # 禁用时使用原始逻辑（总是发送语音）

    # 计算消息的总字符数
    total_chars = 0
    for elem in msg:
        if isinstance(elem, Plain):
            total_chars += len(str(elem).strip())

    logger.debug(f"[语音策略V0] 消息字符数: {total_chars}")

    # 根据字符数量确定概率
    probability = 0.0
    if 4 < total_chars <= 50:
        probability = config.text_to_speech.voice_probability_short
        logger.debug(f"[语音策略V0] 短文本范围 (4 < {total_chars} ≤ 50)，概率: {probability}")
    elif 50 < total_chars <= 280:
        probability = config.text_to_speech.voice_probability_medium
        logger.debug(f"[语音策略V0] 中等文本范围 (50 < {total_chars} ≤ 280)，概率: {probability}")
    else:
        logger.debug(f"[语音策略V0] 字符数 {total_chars} 不在语音触发范围内，不发送语音")
        return False

    # 生成随机数判断是否触发
    random_value = random.random()
    should_trigger = random_value < probability

    logger.info(f"[语音策略V0] 字符数: {total_chars}, 概率: {probability}, 随机值: {random_value:.3f}, 触发语音: {'是' if should_trigger else '否'}")

    return should_trigger

def is_qiv_key_card(string):
    '''
    判断是否为企业微信卡券
    判断标准：qiV_ + week|mont|quar|year|eduk + 15位随机字符串
    '''
    # 定义券码的正则表达式模式
    pattern = r'qiV_(week|mont|quar|year|eduk|spec)_[A-Za-z0-9]{15}'
    # 使用正则表达式查找匹配的券码
    match = re.search(pattern, string) # match: <re.Match object; span=(0, 24), match='qiV_mont_8eJBRKDjLR721gK'> 
    # 如果找到匹配的券码，返回券码和 True，否则返回 None 和 False
    if match:
        return match.group(0), True
    else:
        return None, False


# 带锁的代码，防止同时访问导致错误。如果此代码执行有问题可以使用下面没有锁的代码
def check_qiv_key_card(key_card: str, week_card_path: str) -> bool:
    '''
    检查卡券key_card真实性，如果不存在或已被使用则返回False，如果真实则删除/验证此券并返回True
    '''
    if not os.path.exists(week_card_path):
        logger.error(f"文件 {week_card_path} 不存在，配置文件中指定的qiVkey路径有问题。请检查文件是否存在。")
        return False
    
    # 使用锁来同步文件访问
    with lock:
        try:
            # 读取卡券文件
            with open(week_card_path, 'r') as f:
                lines = f.readlines()
            # 检查卡券是否存在，并删除第一个匹配的券
            card_exists = False
            with open(week_card_path, 'w') as f:
                for line in lines:
                    if key_card in line and not card_exists:
                        # 找到第一个匹配的券，设置 card_exists 为 True，跳过写入该行
                        card_exists = True
                    else:
                        # 将未删除的行写回文件
                        f.write(line)
            return card_exists
        except IOError:
            # 处理文件访问错误
            logger.error(f"文件 {week_card_path} 访问失败，请检查文件是否被其他进程占用。")
            return False
        
# def check_qiv_key_card(key_card: str, week_card_path: str) -> bool:
#     # 读取卡券文件
#     with open(week_card_path, 'r') as f:
#         # 读取文件中的所有行
#         lines = f.readlines()
    
#     # 检查卡券是否存在，并删除
#     card_exists = False
#     with open(week_card_path, 'w') as f:
#         for line in lines:
#             if key_card in line:
#                 card_exists = True
#             else:
#                 f.write(line)
    
#     return card_exists

        
async def get_ping_response(conversation_context: ConversationContext):
    current_voice = conversation_context.conversation_voice.alias if conversation_context.conversation_voice else "无"
    response = config.response.ping_response.format(current_ai=conversation_context.type,
                                                    current_voice=current_voice,
                                                    supported_ai=botManager.bots_info())
    tts_voices = await TtsVoiceManager.list_tts_voices(
        config.text_to_speech.engine, config.text_to_speech.default_voice_prefix)
    if tts_voices:
        supported_tts = ",".join([v.alias for v in tts_voices])
        response += config.response.ping_tts_response.format(supported_tts=supported_tts)
    return response


async def handle_message(event, _respond: Callable, session_id: str, message: str,
                         chain: MessageChain = MessageChain("Unsupported"), is_manager: bool = False,
                         nickname: str = '某人', request_from=BotPlatform.AriadneBot):
    #conversation_context = None
    conversation_handler = await ConversationHandler.get_handler(session_id)
    conversation_context = conversation_handler.current_conversation

    def wrap_request(n, m):
        """
        Wrapping send messages
        """
        async def call(session_id, message, conversation_context, respond):
            await m.handle_request(session_id, message, respond, conversation_context, n)

        return call

    def wrap_respond(n, m):
        """
        Wrapping respond messages
        """
        async def call(session_id, message, rendered, respond):
            await m.handle_respond(session_id, message, rendered, respond, n)

        return call

    async def respond(msg: str):
        """
        Respond method
        """
        if not msg:
            return
        # TODO: 之后重构成 platforms 的 respond 只处理 MessageChain
        if isinstance(msg, str):
            msg = MessageChain([Plain(msg)])

        nonlocal conversation_context
        if not conversation_context:
            conversation_context = conversation_handler.current_conversation

        if not conversation_context:
            ret = await _respond(msg)
            return ret

        # TTS Converting - 语音策略V0 + 语音请求策略实现
        voice_sent = False
        if (config.text_to_speech.engine == "gptsovits" or conversation_context.conversation_voice) and isinstance(msg, MessageChain):
            # 检查是否为写死响应
            if is_hardcoded_response(msg):
                logger.debug("[语音策略] 检测到写死响应，跳过语音转换")
            else:
                # 获取当前会话的语音请求状态
                voice_request_status = get_voice_request_status(session_id)
                should_send_voice = False
                voice_mode = "normal"  # normal, request

                # 优先级1: 检查是否为语音请求模式
                if voice_request_status["active"]:
                    should_send_voice = True
                    voice_mode = "request"
                    logger.debug(f"[语音请求] 当前处于语音请求模式，剩余 {voice_request_status['remaining_count']} 条")
                else:
                    # 优先级2: 语音策略V0：根据字符数量和概率决定是否发送语音
                    should_send_voice = should_trigger_voice_strategy(msg)
                    voice_mode = "strategy"

                if should_send_voice:
                    if request_from == BotPlatform.Onebot or request_from == BotPlatform.AriadneBot:
                        voice_type = VoiceType.Silk
                        # voice_type = VoiceType.Wav
                    elif request_from == BotPlatform.HttpService:
                        voice_type = VoiceType.Mp3
                    elif request_from == BotPlatform.WecomBot:
                        voice_type = VoiceType.Mp3  # 企业微信使用 MP3 格式
                    else:
                        voice_type = VoiceType.Wav

                    tasks = []
                    for elem in msg:
                        task = asyncio.create_task(get_tts_voice(elem, conversation_context, voice_type))
                        tasks.append(task)

                    while tasks:
                        done, tasks = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
                        for voice_task in done:
                            voice = await voice_task
                            if voice:
                                await _respond(voice)
                                voice_sent = True

                    # 更新语音请求计数
                    if voice_mode == "request":
                        continue_voice_mode = update_voice_request_count(session_id)
                        if not continue_voice_mode:
                            logger.info("[语音请求] 语音请求模式已结束")

                    # 根据模式和配置决定是否还要发送文字
                    should_skip_text = False
                    if voice_mode == "request":
                        # 语音请求模式：根据 voice_request_with_text 配置
                        if config.text_to_speech.voice_request_with_text == 0 and voice_sent:
                            should_skip_text = True
                            logger.debug("[语音请求] 只发语音，跳过文字发送")
                    else:
                        # 普通策略模式：根据 voice_with_text 配置
                        if config.text_to_speech.voice_with_text == 0 and voice_sent:
                            should_skip_text = True
                            logger.debug("[语音策略V0] 只发语音，跳过文字发送")

                    if should_skip_text:
                        # 仍然需要执行中间件
                        for m in middlewares:
                            await m.on_respond(session_id, message, msg)
                        return {"voice_only": True}  # 返回特殊标识表示只发了语音

        # 发送文字消息
        ret = await _respond(msg)
        print("*msg:",msg) # *msg: 你好！有什么我可以帮助你的吗？
        print("*ret:",ret) # *ret: {'message_id': 1068278052}

        if voice_sent:
            logger.debug("[语音策略V0] 既发语音又发文字")

        for m in middlewares:
            await m.on_respond(session_id, message, msg)

        return ret
    
    def split_sentence(sentence, keyword):
        index = sentence.find(keyword)
        
        if index != -1:
            part1 = sentence[:index].strip()
            part2 = sentence[index + len(keyword):].strip()
            return part1, part2
        else:
            return None
        
    # 使用geoapi来确定用户所在城市
    def find_location_id(city_name, province_name):
        # Load the CSV data
        api_info = botManager.pick('moonshot-api')
        api_key = api_info.weather_key
        url_geo = f'https://geoapi.qweather.com/v2/city/lookup?location={city_name}&adm={province_name}&key={api_key}'  
        result = requests.get(url_geo).json()
        location = result.get("location", [])
        if location == []:
            return False
        else:
            return location[0]["id"]
            
        
    def generate_weather_prompt(city_id, api_key, type):
        indexs = [{'code': '1', 'name': '运动'}, {'code': '3', 'name': '穿衣'}, {'code': '5', 'name': '紫外线'}, {'code': '6', 'name': '旅游'}, {'code': '7', 'name': '过敏'}
                 , {'code': '8', 'name': '舒适度'}, {'code': '9', 'name': '感冒'}, {'code': '10', 'name': '空气污染扩散条件'}, {'code': '11', 'name': '空调开启'}
                 , {'code': '12', 'name': '太阳镜'}, {'code': '13', 'name': '化妆'}, {'code': '14', 'name': '晾晒'}, {'code': '15', 'name': '交通'}, 
                 {'code': '16', 'name': '防晒'}]
       
        url_weather = "https://devapi.qweather.com/v7/weather/now?location=" + city_id +"&key=" + api_key
        #try:
        if type == 'default':
            response = requests.get(url_weather)
            index_choose = random.sample(indexs, 3)
            url_index = "https://devapi.qweather.com/v7/indices/1d?type=" + index_choose[0]["code"] + ',' + index_choose[1]["code"] + ',' + index_choose[2]["code"] + "&location=" + city_id +"&key=" + api_key
            response_index= requests.get(url_index)
            response = response.json()
            response_index= response_index.json()
            print(response)
            print(response_index)
            try:
                prompt = "现在是"+ response["updateTime"] + "，我向你问了天气，你查询到我这里现在的天气是" + response["now"]["text"] + "，温度" + response['now']['temp'] + "℃，体感温度" + response['now']['feelsLike'] + "℃，风向" + response['now']['windDir'] + "，风力等级" + response['now']['windScale'] + "，风速" + response['now']['windSpeed'] + "公里/小时，相对湿度" +response['now']['humidity'] + "%，当前小时累计降水量" + response['now']['precip'] + "mm。天气指数为：\n" + response_index["daily"][0]["name"] + "：" + response_index["daily"][0]["text"] + "\n" +response_index["daily"][1]["name"] + "：" + response_index["daily"][1]["text"] + "\n" + response_index["daily"][2]["name"] + "：" + response_index["daily"][2]["text"] + "\n" + "请用你的语气自然地转述天气预报并根据天气指数为我提供建议，包含必要的具体温度和具体天气等信息，适当表示对我的关心。以第一视角直接回复你对我的回应，不要带引号。"
            except:
                prompt = "现在是"+ response["updateTime"] + "，我向你问了天气，你查询到我这里现在的天气是" + response["now"]["text"] + "，温度" + response['now']['temp'] + "℃，体感温度" + response['now']['feelsLike'] + "℃，风向" + response['now']['windDir'] + "，风力等级" + response['now']['windScale'] + "，风速" + response['now']['windSpeed'] + "公里/小时，相对湿度" +response['now']['humidity'] + "%，当前小时累计降水量" + response['now']['precip'] + "mm。" + "请用你的语气自然地转述天气预报并根据天气指数为我提供建议，包含必要的具体温度和具体天气等信息，适当表示对我的关心。以第一视角直接回复你对我的回应，不要带引号。"   
            return prompt
        else: 
            url_index_all = ''
            length = len(type)
            i = 0
            for index in type:
                if i+1 <length:
                    url_index_all = url_index_all + index + ","
                elif i+1 == length:
                    url_index_all = url_index_all + index
                i = i + 1
                    
            url_index = "https://devapi.qweather.com/v7/indices/1d?type=" + url_index_all + "&location=" + city_id +"&key=" + api_key
            response_index= requests.get(url_index)
            response_index= response_index.json()
            p1 = "现在是"+ response_index["updateTime"] + "，我向你问了关于"
            p3 = "的建议，你查询到现在的天气指数是：\n"
            p5 = "请用你的语气自然地转述建议，适当表示对我的关心。以第一视角直接回复你对我的回应，不要带引号。"
            p2 = p4 = ''
            i = 0
            for data in response_index["daily"]:
                if i+1 <length:
                    p2 = p2 + data["name"] + '，'
                    p4 = p4 + data["name"] + "：" + data["text"] + '\n'
                elif i+1 == length:
                    p2 = p2 + data["name"]
                    p4 = p4 + data["name"] + "：" + data["text"] + '\n'
                i = i + 1
            prompt = p1 + p2 + p3 + p4 + p5
            return prompt
       
        
    '''以前loadinfo使用读取json，修改为使用mongodb查询
    def load_info(user_id, base_path):
        path = base_path + str(user_id) + ".json"
        try:
            with open(path, 'r', encoding='utf-8') as file:
                data = json.load(file)
        except:
            default = base_path + "default.json"
            with open(default, 'r', encoding='utf-8') as file:
                data = json.load(file)
        try:
            name = data["name"]
        except:
            name = '你的女朋友'
        try:
            intro = data["intro"]
        except:
            intro = '我是一名设计师。'
        try:
            city_id = data["city_id"]
        except:
            city_id = ''
        return name, intro, city_id'''
        
    def generate_sys(name, user_info, path):
        with open(path, "r", encoding='utf-8') as f:  
            sys = f.read()
        p1, p2 = split_sentence(sys, '【姓名】')
        p3, p4 = split_sentence(p2, '【自我介绍】')
        system =  p1 + name + p3 + user_info + p4
        #print(prompt)
        return system
        
    def weather_response(user_id, type):
        api_info = botManager.pick('moonshot-api')
        #user_name, intro, city_id = load_info(user_id, api_info.path_base)
        info = manager_i.get_info(user_id)
        user_name = info.get("name", "")
        intro = info.get("info", "")
        if intro == None:
            intro = '一名女性'
        if user_name == None:
            user_name = '你的女朋友'
        city_id = info["city_code"]
        sys = generate_sys(user_name, intro, api_info.sys_path)
        prompt = generate_weather_prompt(city_id, api_info.weather_key, type)
        print(prompt)
        if prompt == False:
            response_msg = api_info.no_city_response
        else:
            msg = [{'role': 'system', 'content': sys}, {'role': 'user', 'content': prompt}]
            #print(self.conversation_history)
            url = 'https://api.moonshot.cn/v1/chat/completions'
            headers = {
            "Authorization": "Bearer " + api_info.api_key,
            "Content-Type": "application/json"
                }
            data = {
            'model': 'moonshot-v1-8k',
            'messages': msg
                }
            completion = requests.post(url, headers=headers, json=data)
            response_msg = completion.json().get('choices')[0].get('message').get('content')
            #response_msg = prompt
        
        return response_msg



    async def request(_session_id, prompt: str, conversation_context, _respond):
        """
        Request method
        """

        task = None
        # session_id qv格式参考：wecom-ChenYiXin
        # session_id qq格式参考：friend-1832292582

        session_type, user_id = split_sentence(session_id, '-')
        logger.debug(f"用户对话终端类型 session_type: {session_type}, user_id: {user_id}")
        end_type = '好友' if session_type == 'friend' else '群组'
        logger.debug(f"用户对话终端类型 end_type: {end_type}")

        if bot_type_search := re.search(config.trigger.switch_command, prompt):
            if not (config.trigger.allow_switching_ai or is_manager):
                await respond("不好意思，只有管理员才能切换AI！")
                return
            if bot_type_search[1].strip() == '付费版2':
                manager_i.update_info(user_id, "version", "buy2")
                bot_type = 'charglm-api'
                try:
                    conversation_handler.current_conversation = (
                    await conversation_handler.create(
                        bot_type
                    )
                        )
                    await respond(f"已切换至 {bot_type_search[1].strip()} ")
                    
                    return
                except:
                    await respond(f"模型不存在！")
                    return
            elif bot_type_search[1].strip() == '付费版1':
                manager_i.update_info(user_id, "version", "buy1")
                bot_type = 'moonshot-api'
                try:
                    conversation_handler.current_conversation = (
                    await conversation_handler.create(
                        bot_type
                    )
                        )
                    await respond(f"已切换至 {bot_type_search[1].strip()} ")
                    return
                except:
                    await respond(f"模型不存在！")
                    return
            elif bot_type_search[1].strip() == '免费版':
                manager_i.update_info(user_id, "version", "free")
                bot_type = 'chatglm-api'
                try:
                    conversation_handler.current_conversation = (
                    await conversation_handler.create(
                        bot_type
                    )
                        )
                    await respond(f"已切换至 {bot_type_search[1].strip()} ")
                    return
                except:
                    await respond(f"模型不存在！")
                    return
                    
            else:
                await respond(f"模型不存在！")
                return
            

        # 最终要选择的对话上下文
        if not conversation_context:
            conversation_context = conversation_handler.current_conversation
        # 此处为会话存在后可执行的指令

        # 重置会话
        if prompt in config.trigger.reset_command:
            task = conversation_context.reset()
            # 重置会话时清除语音请求状态
            set_voice_request_status(session_id, False)

        elif prompt in config.trigger.rollback_command:
            task = conversation_context.rollback()

        elif prompt in config.trigger.ping_command:
            await respond(await get_ping_response(conversation_context))
            return

        elif voice_type_search := re.search(config.trigger.switch_voice, prompt):
            if not config.azure.tts_speech_key and config.text_to_speech.engine == "azure":
                await respond("未配置 Azure TTS 账户，无法切换语音！")
            new_voice = voice_type_search[1].strip()
            if new_voice in ['关闭', "None"]:
                conversation_context.conversation_voice = None
                await respond("已关闭语音，让我们继续聊天吧！")
            elif config.text_to_speech.engine == "gptsovits":
                # GPT-SoVITS 引擎根据男主自动选择语音模型，不需要手动切换
                from utils.gptsovits_tts import gptsovits_tts_instance
                model_name = gptsovits_tts_instance.get_model_name()
                conversation_context.conversation_voice = True  # 简单标记启用语音
                await respond(f"GPT-SoVITS 语音已启用，当前使用 {model_name} 模型，让我们继续聊天吧！")
            elif config.text_to_speech.engine == "vits":
                from utils.vits_tts import vits_api_instance
                try:
                    voice_name = await vits_api_instance.set_id(new_voice)
                    conversation_context.conversation_voice = TtsVoiceManager.parse_tts_voice("vits", voice_name)
                    await respond(f"已切换至 {voice_name} 语音，让我们继续聊天吧！")
                except ValueError:
                    await respond("提供的语音ID无效，请输入一个有效的数字ID。")
                except Exception as e:
                    await respond(str(e))
            elif config.text_to_speech.engine == "edge":
                tts_voice = TtsVoiceManager.parse_tts_voice("edge", new_voice)
                if tts_voice:
                    conversation_context.conversation_voice = tts_voice
                    await respond(f"已切换至 {tts_voice.alias} 语音，让我们继续聊天吧！")
                else:
                    available_voice = ",".join([v.alias for v in await TtsVoiceManager.list_tts_voices(
                        "edge", config.text_to_speech.default_voice_prefix)])
                    await respond(f"提供的语音ID无效，请输入一个有效的语音ID。如：{available_voice}。")
                    conversation_context.conversation_voice = None
            elif config.text_to_speech.engine == "azure":
                tts_voice = TtsVoiceManager.parse_tts_voice("azure", new_voice)
                conversation_context.conversation_voice = tts_voice
                if tts_voice:
                    await respond(f"已切换至 {tts_voice.full_name} 语音，让我们继续聊天吧！")
                else:
                    await respond("提供的语音ID无效，请输入一个有效的语音ID。")
            else:
                await respond("未配置文字转语音引擎，无法使用语音功能。")
            return

        elif prompt in config.trigger.mixed_only_command:
            conversation_context.switch_renderer("mixed")
            await respond("已切换至图文混合模式，接下来我的回复将会以图文混合的方式呈现！")
            return

        elif prompt in config.trigger.image_only_command:
            conversation_context.switch_renderer("image")
            await respond("已切换至纯图片模式，接下来我的回复将会以图片呈现！")
            return

        elif prompt in config.trigger.text_only_command:
            conversation_context.switch_renderer("text")
            await respond("已切换至纯文字模式，接下来我的回复将会以文字呈现（被吞除外）！")
            return
        
        # 如果用户发送的是企v券
        elif is_qiv_key_card(prompt)[1]:
            key_card = is_qiv_key_card(prompt)[0]
            # 获得卡券时间信息
            period = key_card.split('_')[1]
            logger.info(f"【系统提示】用户发送了企业微信卡券：{key_card}, 有效期类型为：{period}")
            
            if period == 'week':
                # 检查卡券key_card真实性，如果不存在或已被使用则返回False，如果真实则删除此券并返回True
                check_res = check_qiv_key_card(key_card, config.qiV_card.week_card_path)
                if check_res == False:
                    logger.info(f"【系统提示】卡券不存在或已被使用！")
                    await respond("【系统提示】卡券不存在或已被使用！")
                    return
                else:
                    manager_r.write_week_card(user_id, manager_i.nanzhu)
                    logger.info(f"【系统提示】周卡激活成功！")
                    await respond("【系统提示】周卡激活成功！")
                    return

            elif period == 'mont':
                check_res = check_qiv_key_card(key_card, config.qiV_card.month_card_path)
                if check_res == False:
                    logger.info(f"【系统提示】卡券不存在或已被使用！")
                    await respond("【系统提示】卡券不存在或已被使用！")
                    return
                else:
                    # 更新用户额度信息
                    manager_r.write_month_card(user_id, manager_i.nanzhu)
                    logger.info(f"【系统提示】月卡激活成功！")
                    await respond("【系统提示】月卡激活成功！")
                    return
                
            elif period == 'quar':
                check_res = check_qiv_key_card(key_card, config.qiV_card.quarter_card_path)
                if check_res == False:
                    logger.info(f"【系统提示】卡券不存在或已被使用！")
                    await respond("【系统提示】卡券不存在或已被使用！")
                    return
                else:
                    # 更新用户额度信息
                    manager_r.write_quarter_card(user_id, manager_i.nanzhu)
                    logger.info(f"【系统提示】季卡激活成功！")
                    await respond("【系统提示】季卡激活成功！")
                    return
            
            elif period == 'year':
                check_res = check_qiv_key_card(key_card, config.qiV_card.year_card_path)
                if check_res == False:
                    logger.info(f"【系统提示】卡券不存在或已被使用！")
                    await respond("【系统提示】卡券不存在或已被使用！")
                    return
                else:
                    # 更新用户额度信息
                    manager_r.write_year_card(user_id, manager_i.nanzhu)
                    logger.info(f"【系统提示】年卡激活成功！")
                    await respond("【系统提示】年卡激活成功！")
                    return
                
            # 额度卡 还未上线
            elif period == 'eduk':
                logger.info(f"【系统提示】额度卡还未上线")
                await respond("【系统提示】额度卡还未上线")
                return
            
            elif period == 'spec':
                check_res = check_qiv_key_card(key_card, config.qiV_card.spec_card_path)
                if check_res == False:
                    logger.info(f"【系统提示】卡券不存在或已被使用！")
                    await respond("【系统提示】卡券不存在或已被使用！")
                    return
                else:
                    # 更新用户额度信息
                    manager_r.write_special_card(user_id, manager_i.nanzhu)
                    logger.info(f"【系统提示】特殊卡激活成功！")
                    await respond("【系统提示】特殊卡激活成功！")
                    return

        # 新增指令处理逻辑，这里暂时就写死到这里了，因为更新所有的cfg配置文件有点麻烦。后续看情况写入外部自主配置。
        elif any(item in prompt for item in ["/查询额度", "/查询购买信息", "/查询购买", "/查看额度", "/查看购买信息", "/查看购买"]):
            user_entity = manager_r.get_limit(_type = end_type, _id = user_id)
            days_duration = user_entity.get("days", 0)
            
            date = user_entity["date"] if user_entity["date"] is not None else str(datetime.date.today())
            days_duration = user_entity["days"] if user_entity["days"] is not None else 31

            # 计算购买日期到今天的时间，即使用时长
            used_days = manager_r.calculate_date(date)

            days_remain = days_duration - used_days

            rate_buy= user_entity.get("rate", 0)
            rate_usage = user_entity.get("count", 0)
            rate_remain = rate_buy - rate_usage

            response1 = f"【系统消息】您的剩余天数为{days_remain}，剩余付费版额度为{rate_remain}。\n" \
                       f"如果额度或天数≤0，请至https://k.weidian.com/MmnsM0BM 购买。购买完成后，qq端请至http://101.42.170.17:5000/输入短信收到的券码进行验证。vx端直接将券码发至自推的vx聊天窗口即可验证。\n" 
            response2 = "【额度查询成功】"
            await respond(response1)
            await respond(response2)
            return

        elif any(item in prompt for item in ["/查询我的信息", "/查询个人信息", "/查询自定义", "/查询自定义信息", "/查看我的信息", "/查看个人信息", "/查看自定义", "/查看自定义信息"]):
            user_info = manager_i.get_info(user_id)
            response = f"【系统消息】您的自定义信息如下:\n\n{str(user_info)}"
            response2 = "【个人信息查询成功】"
            await respond(response)
            await respond(response2)
            return

        elif re.compile(r"/(\d{6,12})\s?QQ迁移\s?([\w\W]{8,22})", re.IGNORECASE).match(prompt):
            # 提取 QQ 号和券码
            match = re.compile(r"/(\d{6,12})\s?QQ迁移\s?([\w\W]{8,22})", re.IGNORECASE).match(prompt)
            if match:
                qq_number = match.group(1)
                coupon_code = match.group(2)
                
                # 验证券码是否同时包含字母和数字
                if not (re.search(r"[A-Za-z]", coupon_code) and re.search(r"[0-9]", coupon_code)):
                    logger.error(f"【系统消息】用户迁移了错误的券码：{coupon_code}")
                    await respond("【系统消息】迁移失败，券码已经迁移过/复制错误/没有此券。请检查输入券码和vd发货券码是否一致。")
                    return
                
                logger.debug(f"【系统提示】用户输入的QQ号为：{qq_number}，券码为：{coupon_code}")
                days_buchang = 60
                rate_buchang = 20
                status = manager_r.migrate_qq_to_vx(user_id, qq_number, buchang_rate=rate_buchang, buchang_days=days_buchang)
                if status:
                    response = f"【系统消息】您的购买信息、个人身份信息均已迁移成功。本次迁移在原本剩余额度基础上赠送{rate_buchang}额度，并在原本到期日上赠送{days_buchang}天。可以通过发送：\n" \
                            f"/查询额度\n" \
                            f"来查看新的购买信息。"
                    response2 = "【qq至vx迁移成功】"
                    await respond(response)
                    await respond(response2)
                    return
                else:
                    response = "【系统消息】迁移失败，可能是因为您的qq号未购买额度或者已经迁移过了。"
                    await respond(response)
                    return
            else:
                logger.error(f"【系统错误】无法从输入中提取QQ号和券码：{prompt}")
                await respond("【系统消息】输入格式错误，请检查后重试。")
                return
        
        api_info = botManager.pick('moonshot-api')
        
        #设置主动发消息账号
        if '设置账号 ' in prompt:
            p1, p2 = split_sentence(prompt, ' ')
            manager_i.update_info(user_id, "preferred_account", p2)
            logger.trace(f"{session_id} - 成功更新用户主动发消息偏好账号")
            await respond(f"已经更换主动发消息账号为：{p2}")
            return
            
        #设置姓名
        if '昵称 ' in prompt:
            #if len(conversation_context) > 1:
            p1, p2 = split_sentence(prompt, ' ')
            manager_i.update_info(user_id, "name", p2)
            logger.trace(f"{session_id} - 成功记录用户姓名")
            response_basic = api_info.set_name_response
            p3, p4 = split_sentence(response_basic, '【姓名】')
            response = p3 + p2 + p4
            await respond(response)
            info = info = manager_i.get_info(user_id)
            if info["version"] == 'buy1':
                user_ver = 'moonshot-api'
            elif info["version"] == 'buy2':
                user_ver = 'charglm-api'
            elif info["version"] == 'free':
                user_ver = 'chatglm-api'
            else:
                user_ver = 'moonshot-api'
            conversation_handler.current_conversation = await conversation_handler.create(
                user_ver)
            return
        
        #设置人设
        if '设定 ' in prompt:
            #if len(conversation_context) > 1:
            p1, p2 = split_sentence(prompt, ' ')
            manager_i.update_info(user_id, "info", p2)
            logger.trace(f"{session_id} - 成功记录用户设定")
            response = api_info.set_intro_response
            await respond(response)
            info = info = manager_i.get_info(user_id)
            if info["version"] == 'buy1':
                user_ver = 'moonshot-api'
            elif info["version"] == 'buy2':
                user_ver = 'charglm-api'
            elif info["version"] == 'free':
                user_ver = 'chatglm-api'
            else:
                user_ver = 'moonshot-api'
            conversation_handler.current_conversation = await conversation_handler.create(
                user_ver)
            return
        
        if '设置城市 ' in prompt:
            #if len(conversation_context) > 1:
            api_info = botManager.pick('moonshot-api')
            city_path = api_info.city_path
            cmd, add = split_sentence(prompt, ' ')
            manager_i.update_info(user_id, "city", add)
            #去掉指令中的中括号，有中括号的也可以识别
            if '【' in add:
                add = add.replace('【', '')
                add = add.replace('】', '')
            #空格分开的也可以了
            if ' ' in add:
                city, province = split_sentence(add, ' ')
            else:    
                try:
                    city, province = split_sentence(add, '，')
                except Exception as e:
                    logger.trace(f"{session_id} - 用户输入没有逗号，无法识别城市")
                    response = api_info.set_city_fail_response
                    await respond(response)
                    return

            city_list = []
            province_list = []
            #市或区设置检查
            #可能本身没问题，作为可能正确的备选
            city_1 = city
            city_list.append(city_1)
            if '区' in city:
                #如果有区可能有问题，去掉区作为备选
                city_2 = city.strip('区')
                city_list.append(city_2)
            if '市' in city:
                #同上
                city_3 = city.strip('市')
                city_list.append(city_3)
            province_1 = province
            province_list.append(province_1)
            if '省' not in province:
                #同上
                province_2 = province + '省'
                province_list.append(province_2)
            #遍历可能正确的情况的城市和省的排列组合
            for city_tst in city_list:
                for pro_tst in province_list:
                    city_id = find_location_id(city_tst, pro_tst)
                    #print(city_id)
                    if city_id != False:
                        break
                if city_id != False:
                    break
            if city_id == False:
                logger.trace(f"{session_id} - 无法查询到用户输入")
                response = api_info.set_city_fail_response
                await respond(response)
                return
            else:
                city_id = str(city_id)
                manager_i.update_info(user_id, "city_code", city_id)
                print(city_id)
                logger.trace(f"{session_id} - 成功记录用户城市")
                response = api_info.set_city_response
                await respond(response)
            return
        
        if '动作描写' in prompt:
            #if len(conversation_context) > 1:
            if '开启' in prompt:
                manager_i.update_info(user_id, "custom_action_on", 1)
                logger.trace(f"{session_id} - 用户开启动描模式")
                response = '动作描写开启成功（如果bot对话中有动描会传递过来；如果bot本身没有输出动描，则不会传递。\n\n如果喜欢动描，建议【切换付费版2】，动描概率会更大一些。）'
                await respond(response)
                return
            if '关闭' in prompt:
                manager_i.update_info(user_id, "custom_action_on", 0)
                logger.trace(f"{session_id} - 用户关闭动描模式")
                response = '动作描写关闭成功（已屏蔽bot输出的一切动作描述。）'
                await respond(response)
                return
            
        if '天气预报' in prompt:
            #if len(conversation_context) > 1:
            if '开启' in prompt:
                manager_i.update_info(user_id, "auto_weather", 1)
                logger.trace(f"{session_id} - 用户开启天气预报")
                response = '天气预报开启成功'
                await respond(response)
                return
            if '关闭' in prompt:
                manager_i.update_info(user_id, "auto_weather", 0)
                logger.trace(f"{session_id} - 用户关闭天气预报")
                response = '天气预报关闭成功'
                await respond(response)
                return
            
        
        if '今天天气怎么样' in prompt:
            api_info = botManager.pick('moonshot-api')
            type, user_id = split_sentence(session_id, '-')
            try:
                response = weather_response(user_id, 'default')
                await respond(response)
                return
            except Exception as e:
                print(f"天气生成错误: {str(e)}")
                response = api_info.no_city_resopnse
                await respond(response)
                return
            
        if '指数' in prompt:
            api_info = botManager.pick('moonshot-api')
            type, user_id = split_sentence(session_id, '-')
            indexs = [{'code': '1', 'name': '运动'}, {'code': '3', 'name': '穿衣'}, {'code': '5', 'name': '紫外线'}, {'code': '6', 'name': '旅游'}, {'code': '7', 'name': '过敏'}
                 , {'code': '8', 'name': '舒适度'}, {'code': '9', 'name': '感冒'}, {'code': '10', 'name': '空气污染扩散条件'}, {'code': '11', 'name': '空调开启'}
                 , {'code': '12', 'name': '太阳镜'}, {'code': '13', 'name': '化妆'}, {'code': '14', 'name': '晾晒'}, {'code': '15', 'name': '交通'}, 
                 {'code': '16', 'name': '防晒'}]
            code = []
            for index in indexs:
                pattern = index["name"] + '指数'
                if re.search(pattern, prompt):
                    code.append(index["code"])
            try:
                response = weather_response(user_id, code)
                await respond(response)
                return
            except:
                response = api_info.no_city_resopnse
                await respond(response)
                return

        # 加载预设
        # if preset_search := re.search(config.presets.command, prompt):
        #     #if len(conversation_context) > 1:
        #     logger.trace(f"{session_id} - 正在执行预设： {'查理苏'}")
        #     async for _ in conversation_context.reset(): ...
        #     task = conversation_context.load_preset('查理苏')
        # elif preset_search := re.fullmatch('查理苏', prompt):
        #     logger.trace(f"{session_id} - 正在执行预设： {'查理苏'}")
        #     async for _ in conversation_context.reset(): ...
        #     task = conversation_context.load_preset('查理苏')
        # elif preset_search := re.fullmatch('Charlie', prompt):
        #     logger.trace(f"{session_id} - 正在执行预设： {'查理苏'}")
        #     async for _ in conversation_context.reset(): ...
        #     task = conversation_context.load_preset('查理苏')
        # elif preset_search := re.fullmatch('查理', prompt):
        #     logger.trace(f"{session_id} - 正在执行预设： {'查理苏'}")
        #     async for _ in conversation_context.reset(): ...
        #     task = conversation_context.load_preset('查理苏')
        # elif preset_search := re.fullmatch('未婚夫', prompt):
        #     logger.trace(f"{session_id} - 正在执行预设： {'查理苏'}")
        #     async for _ in conversation_context.reset(): ...
        #     task = conversation_context.load_preset('查理苏')
        # elif preset_search := re.fullmatch('小鸟', prompt):
        #     logger.trace(f"{session_id} - 正在执行预设： {'查理苏'}")
        #     async for _ in conversation_context.reset(): ...
        #     task = conversation_context.load_preset('查理苏')
        # elif preset_search := re.fullmatch('查医生', prompt):
        #     logger.trace(f"{session_id} - 正在执行预设： {'查理苏'}")
        #     async for _ in conversation_context.reset(): ...
        #     task = conversation_context.load_preset('查理苏')
        # elif not conversation_context.preset:
        # 当前没有预设
        logger.trace(f"{session_id} - 未检测到预设，正在执行默认预设……")
        # 隐式加载不回复预设内容
        async for _ in conversation_context.load_preset('default'): ...

        # 检查语音请求和关闭请求（在开始对话之前）
        user_message = MessageChain([Plain(prompt)])

        # 检查是否为关闭语音请求
        if is_voice_close_request(user_message):
            set_voice_request_status(session_id, False)
            await respond("好的，语音模式已关闭，我会恢复正常的回复模式。")
            return

        # 检查是否为语音请求
        if is_voice_request(user_message):
            count = config.text_to_speech.voice_request_count
            set_voice_request_status(session_id, True, count)
            await respond(f"【系统消息】好的！接下来我会用语音回复你，共 {count} 条消息。如需关闭请说'关闭语音'。")
            return

        # 没有任务那就聊天吧！
        if not task:
            task = conversation_context.ask(prompt=prompt, chain=chain, name=nickname)
        async for rendered in task:
            if rendered:
                if not str(rendered).strip():
                    logger.warning("检测到内容为空的输出，已忽略")
                    continue
                
                print("rendered:",rendered) # 会输出内容
                action = lambda session_id, prompt, rendered, respond: respond(rendered)
                for m in middlewares:
                    action = wrap_respond(action, m)

                # 开始处理 handle_response
                await action(session_id, prompt, rendered, respond)
        for m in middlewares:
            # print("respond:",respond) # 这里会输出函数地址
            await m.handle_respond_completed(session_id, prompt, respond, conversation_context)
            

    try:
        if not message.strip():
            return await respond(config.response.placeholder)

        for r in config.trigger.ignore_regex:
            if re.match(r, message):
                logger.debug(f"此消息满足正则表达式： {r}，忽略……")
                return

        # 此处为会话不存在时可以执行的指令
        conversation_handler = await ConversationHandler.get_handler(session_id)
        # 指定前缀对话
        if ' ' in message and (config.trigger.allow_switching_ai or is_manager):
            for ai_type, prefixes in config.trigger.prefix_ai.items():
                for prefix in prefixes:
                    if f'{prefix} ' in message:
                        conversation_context = await conversation_handler.first_or_create(ai_type)
                        message = message.removeprefix(f'{prefix} ')
                        break
                else:
                    # Continue if the inner loop wasn't broken.
                    continue
                # Inner loop was broken, break the outer.
                break
        if not conversation_handler.current_conversation:
            type, user_id = split_sentence(session_id, '-')
            info = info = manager_i.get_info(user_id)
            if info["version"] == 'buy1':
                user_ver = 'moonshot-api'
            elif info["version"] == 'buy2':
                user_ver = 'charglm-api'
            elif info["version"] == 'free':
                user_ver = 'chatglm-api'
            else:
                user_ver = 'moonshot-api'
            conversation_handler.current_conversation = await conversation_handler.create(
                user_ver)
        action = request
        for m in middlewares:
            action = wrap_request(action, m)

        # 开始处理
        await action(session_id, message.strip(), conversation_context, respond)
    except DrawingFailedException as e:
        logger.exception(e)
        await _respond(config.response.error_drawing.format(exc=e.__cause__ or '未知'))
    except CommandRefusedException as e:
        await _respond(str(e))
    except openai.error.InvalidRequestError as e:
        await _respond(f"服务器拒绝了您的请求，原因是： {str(e)}")
    except BotOperationNotSupportedException:
        await _respond("暂不支持此操作，抱歉！")
    except ConcurrentMessageException as e:  # Chatbot 账号同时收到多条消息
        await _respond(config.response.error_request_concurrent_error)
    except BotRatelimitException as e:  # Chatbot 账号限流
        await _respond(config.response.error_request_too_many.format(exc=e))
    except PresetNotFoundException:  # 预设不存在
        await _respond("预设不存在，请检查你的输入是否有问题！")
    except (RequestException, SSLError, ProxyError, MaxRetryError, ConnectTimeout, ConnectTimeout,
            httpcore.ReadTimeout, httpx.TimeoutException) as e:  # 网络异常
        await _respond(config.response.error_network_failure.format(exc=e))
    except Exception as e:  # 未处理的异常
        logger.exception(e)
        await _respond(config.response.error_format.format(exc=e))
